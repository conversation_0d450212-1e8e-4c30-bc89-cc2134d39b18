[".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestAsyncToolsAndErrorCases::test_analyze_contextual_results_with_dict_input", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestAsyncToolsAndErrorCases::test_analyze_contextual_results_with_empty_results", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestAsyncToolsAndErrorCases::test_analyze_contextual_results_with_failed_query", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestAsyncToolsAndErrorCases::test_analyze_contextual_results_with_invalid_json_string", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestAsyncToolsAndErrorCases::test_analyze_contextual_results_with_json_string_input", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestAsyncToolsAndErrorCases::test_analyze_contextual_results_with_list_input", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestAsyncToolsAndErrorCases::test_execute_sparql_query_with_invalid_query", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestAsyncToolsAndErrorCases::test_execute_sparql_query_with_valid_query", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestContextualInsight::test_contextual_insight_creation_defaults", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestContextualInsight::test_contextual_insight_creation_invalid_confidence", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestContextualInsight::test_contextual_insight_creation_valid", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestContextualStrategy::test_contextual_strategy_creation_empty_steps", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestContextualStrategy::test_contextual_strategy_creation_valid", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestDataStructures::test_enhanced_ttl_context_creation", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestDataStructures::test_guidelines_context_creation", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestDataStructures::test_ontology_context_creation", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestEdgeCases::test_analyze_guidelines_with_missing_fields", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestEdgeCases::test_analyze_ontology_with_unicode_content", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestEdgeCases::test_format_functions_with_malformed_data", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestEdgeCases::test_format_functions_with_none_values", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestEdgeCases::test_sparql_validation_edge_cases", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestEnhancedSPARQLQuery::test_enhanced_sparql_query_creation_defaults", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestEnhancedSPARQLQuery::test_enhanced_sparql_query_creation_valid", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_class_relationships_empty", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_class_relationships_large", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_class_relationships_valid", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_property_constraints_empty", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_property_constraints_valid", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_property_definitions_empty", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_property_definitions_valid", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_property_types_units_empty", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_property_types_units_valid", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_required_properties_empty", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestFormattingFunctions::test_format_required_properties_valid", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestGuidelinesAnalysis::test_analyze_guidelines_empty_structure", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestGuidelinesAnalysis::test_analyze_guidelines_invalid_json", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestGuidelinesAnalysis::test_analyze_guidelines_malformed_structure", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestGuidelinesAnalysis::test_analyze_guidelines_nonexistent_file", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestGuidelinesAnalysis::test_analyze_guidelines_valid_file", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestOntologyAnalysis::test_analyze_ontology_empty_file", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestOntologyAnalysis::test_analyze_ontology_invalid_ttl", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestOntologyAnalysis::test_analyze_ontology_nonexistent_file", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestOntologyAnalysis::test_analyze_ontology_valid_file", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestSPARQLValidation::test_validate_sparql_query_empty", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestSPARQLValidation::test_validate_sparql_query_invalid_syntax", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestSPARQLValidation::test_validate_sparql_query_none", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestSPARQLValidation::test_validate_sparql_query_valid_ask", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestSPARQLValidation::test_validate_sparql_query_valid_construct", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestSPARQLValidation::test_validate_sparql_query_valid_select", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestTTLAnalysis::test_analyze_ttl_file_basic_invalid", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestTTLAnalysis::test_analyze_ttl_file_basic_nonexistent", ".phases/phase7_dynamic_context_manager/test_unit_comprehensive.py::TestTTLAnalysis::test_analyze_ttl_file_basic_valid"]