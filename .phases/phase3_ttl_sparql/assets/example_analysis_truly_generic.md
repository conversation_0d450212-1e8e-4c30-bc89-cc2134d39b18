# 📊 TTL File Structure Analysis: `example.ttl`

*This analysis provides comprehensive context for understanding what can be queried from this TTL file, without making assumptions about specific vocabularies or data structures.*

## 📋 File Statistics
- **File Size**: 91,423 bytes (89.3 KB)
- **Total RDF Triples**: 2,172
- **Declared Namespaces**: 32

## 🏷️ Declared Namespace Prefixes
*These prefixes are declared in the TTL file and can be used in SPARQL queries:*

| Prefix | Namespace URI |
|--------|---------------|
| `brick` | `https://brickschema.org/schema/Brick#` |
| `csvw` | `http://www.w3.org/ns/csvw#` |
| `dc` | `http://purl.org/dc/elements/1.1/` |
| `dcam` | `http://purl.org/dc/dcam/` |
| `dcat` | `http://www.w3.org/ns/dcat#` |
| `dcmitype` | `http://purl.org/dc/dcmitype/` |
| `dcterms` | `http://purl.org/dc/terms/` |
| `doap` | `http://usefulinc.com/ns/doap#` |
| `foaf` | `http://xmlns.com/foaf/0.1/` |
| `geo` | `http://www.opengis.net/ont/geosparql#` |
| `ibpdi` | `https://ibpdi.datacat.org/class/` |
| `inst` | `https://example.com/` |
| `odrl` | `http://www.w3.org/ns/odrl/2/` |
| `org` | `http://www.w3.org/ns/org#` |
| `owl` | `http://www.w3.org/2002/07/owl#` |
| `prof` | `http://www.w3.org/ns/dx/prof/` |
| `prop` | `https://ibpdi.datacat.org/property/` |
| `prov` | `http://www.w3.org/ns/prov#` |
| `qb` | `http://purl.org/linked-data/cube#` |
| `rdf` | `http://www.w3.org/1999/02/22-rdf-syntax-ns#` |
| `rdfs` | `http://www.w3.org/2000/01/rdf-schema#` |
| `schema` | `https://schema.org/` |
| `sh` | `http://www.w3.org/ns/shacl#` |
| `skos` | `http://www.w3.org/2004/02/skos/core#` |
| `sosa` | `http://www.w3.org/ns/sosa/` |
| `ssn` | `http://www.w3.org/ns/ssn/` |
| `time` | `http://www.w3.org/2006/time#` |
| `vann` | `http://purl.org/vocab/vann/` |
| `void` | `http://rdfs.org/ns/void#` |
| `wgs` | `https://www.w3.org/2003/01/geo/wgs84_pos#` |
| `xml` | `http://www.w3.org/XML/1998/namespace` |
| `xsd` | `http://www.w3.org/2001/XMLSchema#` |

## 🏗️ RDF Classes (Types)
*Classes represent the types of entities in this dataset. Each class can have multiple instances.*

| Class URI | Instance Count |
|-----------|----------------|
| `https://ibpdi.datacat.org/class/Address` | 137 |
| `https://ibpdi.datacat.org/class/Building` | 137 |

### Class URI Pattern Analysis
- **Total distinct classes**: 2
- **Namespace diversity**: 1 different namespaces
- **Most common class namespaces**:
  - `https://ibpdi.datacat.org/class/` (2 classes)

## 🔗 RDF Properties (Relationships)
*Properties define relationships between resources. Each property connects subjects to objects.*

| Property URI | Usage Count | Distinct Subjects | Distinct Objects |
|--------------|-------------|-------------------|------------------|
| `http://www.w3.org/1999/02/22-rdf-syntax-ns#type` | 274 | 274 | 2 |
| `https://ibpdi.datacat.org/property/energy-efficiency-class` | 137 | 137 | 7 |
| `https://ibpdi.datacat.org/property/building-code` | 137 | 137 | 137 |
| `https://ibpdi.datacat.org/property/postal-code` | 137 | 137 | 108 |
| `https://ibpdi.datacat.org/property/construction-year` | 137 | 137 | 52 |
| `https://ibpdi.datacat.org/class/hasBuilding` | 137 | 137 | 137 |
| `https://ibpdi.datacat.org/property/valid-from` | 137 | 137 | 1 |
| `https://ibpdi.datacat.org/property/primary-type-of-building` | 137 | 137 | 4 |
| `https://ibpdi.datacat.org/property/name` | 137 | 137 | 134 |
| `https://ibpdi.datacat.org/property/primary-heating-type` | 137 | 137 | 2 |
| `https://ibpdi.datacat.org/property/country` | 137 | 137 | 16 |
| `https://ibpdi.datacat.org/property/city` | 136 | 136 | 65 |
| `https://ibpdi.datacat.org/property/parking-spaces` | 136 | 136 | 88 |
| `https://ibpdi.datacat.org/property/street-name` | 133 | 133 | 130 |
| `https://ibpdi.datacat.org/property/house-number` | 123 | 123 | 82 |

### Property URI Pattern Analysis
- **Total distinct properties**: 15
- **Namespace diversity**: 3 different namespaces
- **Most common property namespaces**:
  - `https://ibpdi.datacat.org/property/` (13 properties)
  - `http://www.w3.org/1999/02/22-rdf-syntax-ns#` (1 properties)
  - `https://ibpdi.datacat.org/class/` (1 properties)

## 📊 Data Value Types
*Analysis of what types of values appear as objects in triples.*

| Value Type | Count | Percentage |
|------------|-------|------------|
| Literal | 2,172 | 100.0% |

### Literal Data Types
*Specific data types found in literal values:*

| Data Type | Count |
|-----------|-------|
| `http://www.w3.org/2001/XMLSchema#string` | 1761 |

## 🔄 Relationship Patterns
*How different classes relate to each other through properties.*

| Subject Class | Property | Object Class | Occurrences |
|---------------|----------|--------------|-------------|
| `https://ibpdi.datacat.org/class/Address` | `https://ibpdi.datacat.org/class/hasBuilding` | `https://ibpdi.datacat.org/class/Building` | 137 |

## 🎯 Query Possibilities
*Based on this analysis, here are the types of queries that are possible with this dataset:*

### Generic SPARQL Query Patterns

#### 1. Find All Classes
```sparql
SELECT DISTINCT ?class WHERE {
    ?instance a ?class .
}
```

#### 2. Find All Properties
```sparql
SELECT DISTINCT ?property WHERE {
    ?subject ?property ?object .
}
```

#### 3. Explore Instances of Any Class
```sparql
SELECT ?instance WHERE {
    ?instance a <REPLACE_WITH_CLASS_URI> .
}
LIMIT 10
```

#### 4. Find Properties of Specific Instances
```sparql
SELECT ?property ?value WHERE {
    <REPLACE_WITH_INSTANCE_URI> ?property ?value .
}
```

#### 5. Explore Relationships Between Classes
```sparql
SELECT ?subject ?property ?object WHERE {
    ?subject a <REPLACE_WITH_SUBJECT_CLASS> .
    ?subject ?property ?object .
    ?object a <REPLACE_WITH_OBJECT_CLASS> .
}
```

## 📈 Summary Insights
- **Data Complexity**: High (2,172 triples)
- **Schema Richness**: 2 classes, 15 properties
- **Relationship Patterns**: 1 distinct class-to-class relationships
- **Namespace Usage**: 32 declared, with usage across 4 different namespaces

### Recommended Query Strategy
- **Start with class exploration**: Use the class list to understand entity types
- **Property-based queries**: Rich property set allows for detailed filtering
- **Relationship traversal**: Follow connections between different entity types
- **Use declared prefixes**: Leverage namespace prefixes for cleaner queries

---
*📊 This analysis provides a complete structural understanding of the TTL file without making assumptions about specific vocabularies or data models. Use the URIs and patterns identified above to construct queries appropriate for your specific use case.*