# 📊 TTL File Analysis: `example.ttl`

## 📋 File Overview
- **📁 File Path**: `assets\example.ttl`
- **📏 File Size**: 91,423 bytes (89.3 KB)
- **🔗 Total Triples**: 2,172
- **📚 Namespaces**: 32

## 🏷️ Namespaces and Prefixes
| Prefix | Namespace |
|--------|-----------|
| `brick` | `https://brickschema.org/schema/Brick#` |
| `csvw` | `http://www.w3.org/ns/csvw#` |
| `dc` | `http://purl.org/dc/elements/1.1/` |
| `dcam` | `http://purl.org/dc/dcam/` |
| `dcat` | `http://www.w3.org/ns/dcat#` |
| `dcmitype` | `http://purl.org/dc/dcmitype/` |
| `dcterms` | `http://purl.org/dc/terms/` |
| `doap` | `http://usefulinc.com/ns/doap#` |
| `foaf` | `http://xmlns.com/foaf/0.1/` |
| `geo` | `http://www.opengis.net/ont/geosparql#` |
| `ibpdi` | `https://ibpdi.datacat.org/class/` |
| `inst` | `https://example.com/` |
| `odrl` | `http://www.w3.org/ns/odrl/2/` |
| `org` | `http://www.w3.org/ns/org#` |
| `owl` | `http://www.w3.org/2002/07/owl#` |
| `prof` | `http://www.w3.org/ns/dx/prof/` |
| `prop` | `https://ibpdi.datacat.org/property/` |
| `prov` | `http://www.w3.org/ns/prov#` |
| `qb` | `http://purl.org/linked-data/cube#` |
| `rdf` | `http://www.w3.org/1999/02/22-rdf-syntax-ns#` |
| `rdfs` | `http://www.w3.org/2000/01/rdf-schema#` |
| `schema` | `https://schema.org/` |
| `sh` | `http://www.w3.org/ns/shacl#` |
| `skos` | `http://www.w3.org/2004/02/skos/core#` |
| `sosa` | `http://www.w3.org/ns/sosa/` |
| `ssn` | `http://www.w3.org/ns/ssn/` |
| `time` | `http://www.w3.org/2006/time#` |
| `vann` | `http://purl.org/vocab/vann/` |
| `void` | `http://rdfs.org/ns/void#` |
| `wgs` | `https://www.w3.org/2003/01/geo/wgs84_pos#` |
| `xml` | `http://www.w3.org/XML/1998/namespace` |
| `xsd` | `http://www.w3.org/2001/XMLSchema#` |

## 🏗️ Classes and Instances
| Class | Instances | Percentage |
|-------|-----------|------------|
| `ibpdi:Address` | 137 | 50.0% |
| `ibpdi:Building` | 137 | 50.0% |

## 🔗 Properties and Usage
| Property | Usage | Subjects | Objects |
|----------|-------|----------|---------|
| `rdf:type` | 274 | 274 | 2 |
| `prop:energy-efficiency-class` | 137 | 137 | 7 |
| `prop:building-code` | 137 | 137 | 137 |
| `prop:postal-code` | 137 | 137 | 108 |
| `prop:construction-year` | 137 | 137 | 52 |
| `ibpdi:hasBuilding` | 137 | 137 | 137 |
| `prop:valid-from` | 137 | 137 | 1 |
| `prop:primary-type-of-building` | 137 | 137 | 4 |
| `prop:name` | 137 | 137 | 134 |
| `prop:primary-heating-type` | 137 | 137 | 2 |
| `prop:country` | 137 | 137 | 16 |
| `prop:city` | 136 | 136 | 65 |
| `prop:parking-spaces` | 136 | 136 | 88 |
| `prop:street-name` | 133 | 133 | 130 |
| `prop:house-number` | 123 | 123 | 82 |

## 🎯 Key Insights
- **Classes**: 2 distinct types
- **Properties**: 15 different relationships
- **Vocabulary**: 32 namespace prefixes
- **Complexity**: High (2,172 triples)

## 📝 Sample SPARQL Queries

### Get all `ibpdi:Address` instances
```sparql
PREFIX inst: <https://example.com/>
PREFIX ibpdi: <https://ibpdi.datacat.org/class/>

SELECT ?instance WHERE {
    ?instance a <https://ibpdi.datacat.org/class/Address> .
}
LIMIT 10
```

### Find values of `prop:energy-efficiency-class`
```sparql
PREFIX prop: <https://ibpdi.datacat.org/property/>

SELECT ?subject ?value WHERE {
    ?subject <https://ibpdi.datacat.org/property/energy-efficiency-class> ?value .
}
LIMIT 10
```

### Main relationship: `ibpdi:Address` → `ibpdi:hasBuilding` → `ibpdi:Building`
```sparql
SELECT ?subject ?object WHERE {
    ?subject a <https://ibpdi.datacat.org/class/Address> .
    ?subject <https://ibpdi.datacat.org/class/hasBuilding> ?object .
    ?object a <https://ibpdi.datacat.org/class/Building> .
}
```

---
*📊 Analysis completed successfully. This report provides comprehensive context for AI agents to understand the TTL file structure and generate accurate SPARQL queries.*