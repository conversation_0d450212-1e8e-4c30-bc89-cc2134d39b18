{"cells": [{"cell_type": "markdown", "id": "3a3a12c6", "metadata": {}, "source": ["# Graph Data Understanding and Analysis\n", "\n", "This notebook performs a comprehensive analysis of the RDF graph data contained in `response_1755871648982.json` to understand:\n", "\n", "1. **Data Structure**: How the JSON objects are organized\n", "2. **Graph Data Formats**: RDF/Turtle format and relationships\n", "3. **Metadata <PERSON>s**: Different types and structures of metadata\n", "4. **Entity Relationships**: How Address and Building entities relate\n", "5. **Data Quality**: Missing values, consistency, and completeness\n", "\n", "## Objectives\n", "- Understand the overall structure and patterns in the dataset\n", "- Analyze the RDF graph data and its semantic relationships\n", "- Examine metadata variations and property distributions\n", "- Identify data quality issues and anomalies"]}, {"cell_type": "code", "execution_count": 1, "id": "ef5feeaa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Libraries imported successfully!\n", "Python environment configured with virtual environment\n"]}], "source": ["# Import required libraries\n", "import json\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from collections import Counter, defaultdict\n", "import re\n", "from rdflib import Graph, Namespace\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# Set up plotting style\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"Libraries imported successfully!\")\n", "print(\"Python environment configured with virtual environment\")"]}, {"cell_type": "markdown", "id": "f0177f5b", "metadata": {}, "source": ["## 1. Data Loading and Initial Exploration\n", "\n", "First, let's load the JSON data and examine its basic structure."]}, {"cell_type": "code", "execution_count": 2, "id": "d105247a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Successfully loaded JSON data\n", "📊 Total number of records: 137\n", "📋 Data type: <class 'list'>\n"]}], "source": ["# Load the JSON data\n", "file_path = r\"response_1755871648982.json\"\n", "\n", "try:\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        data = json.load(f)\n", "    print(f\"✅ Successfully loaded JSON data\")\n", "    print(f\"📊 Total number of records: {len(data)}\")\n", "    print(f\"📋 Data type: {type(data)}\")\n", "except FileNotFoundError:\n", "    print(f\"❌ File not found: {file_path}\")\n", "    print(\"Please check the file path\")\n", "except json.JSONDecodeError as e:\n", "    print(f\"❌ JSON decode error: {e}\")\n", "except Exception as e:\n", "    print(f\"❌ Error loading data: {e}\")"]}, {"cell_type": "code", "execution_count": 3, "id": "ded9e456", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 STRUCTURE ANALYSIS\n", "==================================================\n", "Keys in first record: ['graphTemplate', 'graphData', 'accessRights', 'useCase', 'graphMetadata']\n", "\n", "Record 1 structure:\n", "  • graphTemplate: str - @prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .\n", "  • graphData: str - @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\\r\\n@prefix rdfs: <http://www.w3.org/2000/0...\n", "  • accessRights: NoneType - None\n", "  • useCase: NoneType - None\n", "  • graphMetadata: list - List with 2 items\n", "\n", "Record 11 structure:\n", "  • graphTemplate: str - @prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .\n", "  • graphData: str - @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\\r\\n@prefix rdfs: <http://www.w3.org/2000/0...\n", "  • accessRights: NoneType - None\n", "  • useCase: NoneType - None\n", "  • graphMetadata: list - List with 2 items\n", "\n", "Record 51 structure:\n", "  • graphTemplate: str - @prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .\n", "  • graphData: str - @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\\r\\n@prefix rdfs: <http://www.w3.org/2000/0...\n", "  • accessRights: NoneType - None\n", "  • useCase: NoneType - None\n", "  • graphMetadata: list - List with 2 items\n", "\n", "Record 69 structure:\n", "  • graphTemplate: str - @prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .\n", "  • graphData: str - @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\\r\\n@prefix rdfs: <http://www.w3.org/2000/0...\n", "  • accessRights: NoneType - None\n", "  • useCase: NoneType - None\n", "  • graphMetadata: list - List with 2 items\n", "\n", "Record 0 structure:\n", "  • graphTemplate: str - @prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .\n", "  • graphData: str - @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\\r\\n@prefix rdfs: <http://www.w3.org/2000/0...\n", "  • accessRights: NoneType - None\n", "  • useCase: NoneType - None\n", "  • graphMetadata: list - List with 2 items\n", "\n"]}], "source": ["# Examine the structure of individual records\n", "print(\"🔍 STRUCTURE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Check the first few records\n", "if data:\n", "    # Examine keys in the first record\n", "    first_record = data[0]\n", "    print(f\"Keys in first record: {list(first_record.keys())}\")\n", "    print()\n", "    \n", "    # Sample a few records to understand structure variations\n", "    sample_indices = [0, 10, 50, len(data)//2, -1] if len(data) > 50 else [0, len(data)//2, -1]\n", "    \n", "    for i, idx in enumerate(sample_indices):\n", "        if idx < len(data):\n", "            record = data[idx]\n", "            print(f\"Record {idx + 1} structure:\")\n", "            for key, value in record.items():\n", "                if isinstance(value, str):\n", "                    preview = value[:100] + \"...\" if len(value) > 100 else value\n", "                    preview = preview.replace('\\n', '\\\\n').replace('\\r', '\\\\r')\n", "                elif isinstance(value, list):\n", "                    preview = f\"List with {len(value)} items\"\n", "                else:\n", "                    preview = str(value)\n", "                print(f\"  • {key}: {type(value).__name__} - {preview}\")\n", "            print()\n", "else:\n", "    print(\"❌ No data found in file\")"]}, {"cell_type": "code", "execution_count": 4, "id": "518c0be6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📈 DATA QUALITY ANALYSIS\n", "==================================================\n", "\n", "🔸 Field: graphTemplate\n", "   Completeness: 100.0% (137/137)\n", "   Types: {'str': 137}\n", "   Samples: ['@prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .', '@prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .', '@prefix ex: <http://example.org/> .\\nNode1 hasRelation Node2 .']\n", "\n", "🔸 Field: graphData\n", "   Completeness: 100.0% (137/137)\n", "   Types: {'str': 137}\n", "   Samples: ['@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\\r\\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\\r\\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\\r\\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\\r\\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\\r\\n@prefix inst: <https://example.com/>.\\r\\n\\r\\ninst:bf0bf34a-a3a2-4616-a0f7-811a11641cc2 ibpdi:hasBuilding inst:57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2.\\r\\n', '@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\\r\\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\\r\\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\\r\\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\\r\\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\\r\\n@prefix inst: <https://example.com/>.\\r\\n\\r\\ninst:1b0cf251-2bb1-4a54-a669-3dd89d3d1412 ibpdi:hasBuilding inst:10d7c8c3-bc2f-4166-97d7-d11f04a39d82.\\r\\n', '@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\\r\\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\\r\\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\\r\\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\\r\\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\\r\\n@prefix inst: <https://example.com/>.\\r\\n\\r\\ninst:b57b570f-8710-434f-b2f3-eaa809241db0 ibpdi:hasBuilding inst:be0f3b3e-c7ed-4178-8cc4-6114ad605936.\\r\\n']\n", "\n", "🔸 Field: accessRights\n", "   Completeness: 0.0% (0/137)\n", "   Types: {}\n", "\n", "🔸 Field: useCase\n", "   Completeness: 0.0% (0/137)\n", "   Types: {}\n", "\n", "🔸 Field: graphMetadata\n", "   Completeness: 100.0% (137/137)\n", "   Types: {'list': 137}\n", "   Samples: ['List[2]', 'List[2]', 'List[2]']\n"]}], "source": ["# Analyze field completeness and data quality\n", "print(\"📈 DATA QUALITY ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Count field presence and types\n", "field_analysis = {}\n", "for i, record in enumerate(data):\n", "    for key, value in record.items():\n", "        if key not in field_analysis:\n", "            field_analysis[key] = {\n", "                'total_count': 0,\n", "                'non_null_count': 0,\n", "                'null_count': 0,\n", "                'types': Counter(),\n", "                'sample_values': []\n", "            }\n", "        \n", "        field_analysis[key]['total_count'] += 1\n", "        \n", "        if value is None:\n", "            field_analysis[key]['null_count'] += 1\n", "        else:\n", "            field_analysis[key]['non_null_count'] += 1\n", "            field_analysis[key]['types'][type(value).__name__] += 1\n", "            \n", "            # Collect sample values\n", "            if len(field_analysis[key]['sample_values']) < 3:\n", "                if isinstance(value, (str, int, float, bool)):\n", "                    field_analysis[key]['sample_values'].append(value)\n", "                elif isinstance(value, list):\n", "                    field_analysis[key]['sample_values'].append(f\"List[{len(value)}]\")\n", "                else:\n", "                    field_analysis[key]['sample_values'].append(str(type(value)))\n", "\n", "# Display field analysis\n", "for field, stats in field_analysis.items():\n", "    completeness = (stats['non_null_count'] / stats['total_count']) * 100\n", "    print(f\"\\n🔸 Field: {field}\")\n", "    print(f\"   Completeness: {completeness:.1f}% ({stats['non_null_count']}/{stats['total_count']})\")\n", "    print(f\"   Types: {dict(stats['types'])}\")\n", "    if stats['sample_values']:\n", "        print(f\"   Samples: {stats['sample_values']}\")"]}, {"cell_type": "markdown", "id": "e02d0896", "metadata": {}, "source": ["## 2. RDF Graph Data Analysis\n", "\n", "Now let's analyze the RDF/Turtle graph data to understand the semantic relationships and patterns."]}, {"cell_type": "code", "execution_count": 5, "id": "99cc98ff", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🌐 RDF GRAPH ANALYSIS\n", "==================================================\n", "Graph Templates: 1 unique templates\n", "Graph Data Records: 137\n", "Relationships Found: 137\n", "\n", "🔍 Unique Graph Templates:\n", "1. @prefix ex: <http://example.org/> .\n", "Node1 hasRelation Node2 .\n", "\n", "🔗 Sample Relationships:\n", "1. inst:bf0bf34a-a3a2-4616-a0f7-811a11641cc2 ibpdi:hasBuilding inst:57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2.\n", "2. inst:1b0cf251-2bb1-4a54-a669-3dd89d3d1412 ibpdi:hasBuilding inst:10d7c8c3-bc2f-4166-97d7-d11f04a39d82.\n", "3. inst:b57b570f-8710-434f-b2f3-eaa809241db0 ibpdi:hasBuilding inst:be0f3b3e-c7ed-4178-8cc4-6114ad605936.\n", "4. inst:7fe74459-5abc-42f4-99c4-d51f6eb05c97 ibpdi:hasBuilding inst:1f1024af-c0f0-44f6-881b-77dae01bbd6a.\n", "5. inst:018ac746-2503-4f2e-b681-8cb37d2f0e29 ibpdi:hasBuilding inst:1892ebb9-9532-4dee-bddc-a7044da700a2.\n", "6. inst:7c1f5556-2aa4-420f-a504-dd1b2ed9984a ibpdi:hasBuilding inst:c7848807-75bd-4dc2-8bec-9df8c7949fe2.\n", "7. inst:1830a091-367f-434a-b2eb-4e495c16d3ff ibpdi:hasBuilding inst:57cd12c2-f87c-48cf-be65-66574c335774.\n", "8. inst:d036555b-4752-44a8-bad4-b4bdf9c3c6b5 ibpdi:hasBuilding inst:b663e664-9bcb-4adb-afb0-b3f7118ecec5.\n", "9. inst:638051f6-99d2-4784-8907-c9488441ab6a ibpdi:hasBuilding inst:d3954c67-c035-4b3e-96bc-29515c31b7f5.\n", "10. inst:7ff07085-d6c9-4e05-8a75-9f609aa16c58 ibpdi:hasBuilding inst:65c6d94e-ba3f-4c7e-8c49-942e1712cf1e.\n", "\n", "📊 Relationship Types (Predicates):\n", "   ibpdi:hasBuilding: 137 occurrences\n"]}], "source": ["# Analyze RDF Graph Data\n", "print(\"🌐 RDF GRAPH ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Extract and analyze graph templates\n", "graph_templates = []\n", "graph_data_samples = []\n", "relationships = []\n", "\n", "for i, record in enumerate(data):\n", "    if 'graphTemplate' in record and record['graphTemplate']:\n", "        graph_templates.append(record['graphTemplate'])\n", "    \n", "    if 'graphData' in record and record['graphData']:\n", "        graph_data_samples.append(record['graphData'])\n", "        \n", "        # Parse relationships from graph data\n", "        # Look for patterns like \"inst:id1 predicate inst:id2\"\n", "        graph_content = record['graphData']\n", "        lines = graph_content.split('\\n')\n", "        for line in lines:\n", "            line = line.strip()\n", "            if line and not line.startswith('@prefix') and not line.startswith('#'):\n", "                # Extract relationship patterns\n", "                if 'ibpdi:' in line:\n", "                    relationships.append(line.strip())\n", "\n", "print(f\"Graph Templates: {len(set(graph_templates))} unique templates\")\n", "print(f\"Graph Data Records: {len(graph_data_samples)}\")\n", "print(f\"Relationships Found: {len(relationships)}\")\n", "\n", "# Analyze unique graph templates\n", "unique_templates = list(set(graph_templates))\n", "print(f\"\\n🔍 Unique Graph Templates:\")\n", "for i, template in enumerate(unique_templates):\n", "    print(f\"{i+1}. {template}\")\n", "\n", "# Analyze relationship patterns\n", "if relationships:\n", "    print(f\"\\n🔗 Sample Relationships:\")\n", "    for i, rel in enumerate(relationships[:10]):  # Show first 10\n", "        print(f\"{i+1}. {rel}\")\n", "    \n", "    # Extract relationship types (predicates)\n", "    predicates = []\n", "    for rel in relationships:\n", "        # Find patterns like \"ibpdi:hasBuilding\"\n", "        if 'ibpdi:' in rel:\n", "            parts = rel.split()\n", "            for part in parts:\n", "                if part.startswith('ibpdi:') and not part.endswith('.'):\n", "                    predicates.append(part)\n", "    \n", "    print(f\"\\n📊 Relationship Types (Predicates):\")\n", "    predicate_counts = Counter(predicates)\n", "    for pred, count in predicate_counts.most_common():\n", "        print(f\"   {pred}: {count} occurrences\")"]}, {"cell_type": "markdown", "id": "2ff0356c", "metadata": {}, "source": ["## 3. Metadata Structure Analysis\n", "\n", "Let's analyze the `graphMetadata` field to understand entity types, properties, and their patterns."]}, {"cell_type": "code", "execution_count": 6, "id": "af53f660", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 METADATA STRUCTURE ANALYSIS\n", "==================================================\n", "Total Entities: 274\n", "\n", "🏷️ Entity Class Types:\n", "   Address: 137 entities\n", "   Building: 137 entities\n", "\n", "📋 Properties by Class Type:\n", "\n", "   Address (137 entities):\n", "     • HouseNumber: 123/137 (89.8%)\n", "     • Country: 137/137 (100.0%)\n", "     • City: 136/137 (99.3%)\n", "     • PostalCode: 137/137 (100.0%)\n", "     • StreetName: 133/137 (97.1%)\n", "\n", "   Building (137 entities):\n", "     • PrimaryTypeOfBuilding: 137/137 (100.0%)\n", "     • EnergyEfficiencyClass: 137/137 (100.0%)\n", "     • ConstructionYear: 137/137 (100.0%)\n", "     • PrimaryHeatingType: 137/137 (100.0%)\n", "     • BuildingCode: 137/137 (100.0%)\n", "     • ParkingSpaces: 136/137 (99.3%)\n", "     • Name: 137/137 (100.0%)\n", "     • ValidFrom: 137/137 (100.0%)\n"]}], "source": ["# Analyze GraphMetadata Structure\n", "print(\"📊 METADATA STRUCTURE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Extract all metadata entities\n", "all_entities = []\n", "class_types = []\n", "properties_by_class = defaultdict(list)\n", "property_frequencies = defaultdict(Counter)\n", "\n", "for record in data:\n", "    if 'graphMetadata' in record and record['graphMetadata']:\n", "        for entity in record['graphMetadata']:\n", "            all_entities.append(entity)\n", "            \n", "            # Extract class type\n", "            if 'classType' in entity:\n", "                class_type = entity['classType']\n", "                class_types.append(class_type)\n", "                \n", "                # Extract properties for this class\n", "                if 'propertiesValues' in entity and entity['propertiesValues']:\n", "                    props = entity['propertiesValues']\n", "                    properties_by_class[class_type].extend(list(props.keys()))\n", "                    \n", "                    # Count property frequencies by class\n", "                    for prop_name, prop_value in props.items():\n", "                        property_frequencies[class_type][prop_name] += 1\n", "\n", "print(f\"Total Entities: {len(all_entities)}\")\n", "\n", "# Analyze class types\n", "class_type_counts = Counter(class_types)\n", "print(f\"\\n🏷️ Entity Class Types:\")\n", "for class_type, count in class_type_counts.most_common():\n", "    class_name = class_type.split('/')[-1] if '/' in class_type else class_type\n", "    print(f\"   {class_name}: {count} entities\")\n", "\n", "# Analyze properties by class\n", "print(f\"\\n📋 Properties by Class Type:\")\n", "for class_type, props in properties_by_class.items():\n", "    class_name = class_type.split('/')[-1] if '/' in class_type else class_type\n", "    unique_props = list(set(props))\n", "    print(f\"\\n   {class_name} ({class_type_counts[class_type]} entities):\")\n", "    \n", "    # Show property frequencies for this class\n", "    for prop in unique_props:\n", "        frequency = property_frequencies[class_type][prop]\n", "        percentage = (frequency / class_type_counts[class_type]) * 100\n", "        print(f\"     • {prop}: {frequency}/{class_type_counts[class_type]} ({percentage:.1f}%)\")"]}, {"cell_type": "code", "execution_count": 7, "id": "e178b7b0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔍 PROPERTY VALUE ANALYSIS\n", "==================================================\n", "\n", "🏗️ ADDRESS PROPERTIES:\n", "\n", "   📌 StreetName:\n", "      Total: 133 | Unique: 130 | Missing: 0\n", "      Sample values: ['Barthstraße', 'Friedrichstraße', 'Reinhardtstraße', 'Cargo City Sud Gebiude', 'Mainzer Landstraße']\n", "\n", "   📌 HouseNumber:\n", "      Total: 123 | Unique: 82 | Missing: 0\n", "      Sample values: ['12', '50-55', '52', '571', '172-190']\n", "\n", "   📌 PostalCode:\n", "      Total: 137 | Unique: 108 | Missing: 0\n", "      Sample values: ['80339', '10117', '10117', '60549', '60327']\n", "\n", "   📌 City:\n", "      Total: 136 | Unique: 65 | Missing: 0\n", "      Sample values: ['München', 'Berlin', 'Berlin', 'Frankfurt am Main', 'Frankfurt am Main']\n", "\n", "   📌 Country:\n", "      Total: 137 | Unique: 16 | Missing: 0\n", "      Top values:\n", "        • 'Germany': 53 (38.7%)\n", "        • 'France': 14 (10.2%)\n", "        • 'UK': 12 (8.8%)\n", "        • 'Netherlands': 10 (7.3%)\n", "        • 'Italy': 9 (6.6%)\n", "\n", "🏗️ BUILDING PROPERTIES:\n", "\n", "   📌 PrimaryTypeOfBuilding:\n", "      Total: 137 | Unique: 4 | Missing: 0\n", "      Top values:\n", "        • 'Office': 105 (76.6%)\n", "        • 'Retail': 13 (9.5%)\n", "        • 'Hotel': 10 (7.3%)\n", "        • 'Industrial, Distribution Warehouse': 9 (6.6%)\n", "\n", "   📌 BuildingCode:\n", "      Total: 137 | Unique: 137 | Missing: 0\n", "      Range: 1000000001.0 - 1000000137.0\n", "      Sample values: ['1000000001', '1000000002', '1000000003', '1000000004', '1000000005']\n", "\n", "   📌 Name:\n", "      Total: 137 | Unique: 134 | Missing: 0\n", "      Sample values: ['RON<PERSON><PERSON>', 'Checkpoint Charlie', 'Waterfalls Berlin', 'AIR CARGO Center', 'Atrium Plaza']\n", "\n", "   📌 ValidFrom:\n", "      Total: 137 | Unique: 1 | Missing: 0\n", "      Top values:\n", "        • '44926': 137 (100.0%)\n", "\n", "   📌 PrimaryHeatingType:\n", "      Total: 137 | Unique: 2 | Missing: 0\n", "      Top values:\n", "        • 'District heating': 69 (50.4%)\n", "        • 'Natural gas': 68 (49.6%)\n", "\n", "   📌 ParkingSpaces:\n", "      Total: 136 | Unique: 88 | Missing: 0\n", "      Range: 0.0 - 1701.0\n", "      Sample values: ['324', '207', '71', '0', '151']\n", "\n", "   📌 EnergyEfficiencyClass:\n", "      Total: 137 | Unique: 7 | Missing: 0\n", "      Top values:\n", "        • 'k': 84 (61.3%)\n", "        • 'C': 16 (11.7%)\n", "        • 'D': 14 (10.2%)\n", "        • 'B': 10 (7.3%)\n", "        • 'E': 6 (4.4%)\n", "\n", "   📌 ConstructionYear:\n", "      Total: 137 | Unique: 52 | Missing: 0\n", "      Range: 1800.0 - 2022.0\n", "      Sample values: ['2001', '1999', '2003', '2003', '2003']\n"]}], "source": ["# Analyze property values and patterns\n", "print(\"🔍 PROPERTY VALUE ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Collect all property values by class and property\n", "property_values = defaultdict(lambda: defaultdict(list))\n", "\n", "for record in data:\n", "    if 'graphMetadata' in record and record['graphMetadata']:\n", "        for entity in record['graphMetadata']:\n", "            if 'classType' in entity and 'propertiesValues' in entity:\n", "                class_type = entity['classType'].split('/')[-1]\n", "                for prop_name, prop_value in entity['propertiesValues'].items():\n", "                    property_values[class_type][prop_name].append(prop_value)\n", "\n", "# Analyze key properties for each class\n", "for class_name, props in property_values.items():\n", "    print(f\"\\n🏗️ {class_name.upper()} PROPERTIES:\")\n", "    \n", "    for prop_name, values in props.items():\n", "        # Remove None values for analysis\n", "        clean_values = [v for v in values if v is not None]\n", "        unique_values = list(set(clean_values))\n", "        \n", "        print(f\"\\n   📌 {prop_name}:\")\n", "        print(f\"      Total: {len(values)} | Unique: {len(unique_values)} | Missing: {len(values) - len(clean_values)}\")\n", "        \n", "        # Show distribution for categorical data\n", "        if len(unique_values) <= 20 and len(clean_values) > 0:\n", "            value_counts = Counter(clean_values)\n", "            print(f\"      Top values:\")\n", "            for value, count in value_counts.most_common(5):\n", "                percentage = (count / len(clean_values)) * 100\n", "                print(f\"        • '{value}': {count} ({percentage:.1f}%)\")\n", "        \n", "        # Show range for numerical data\n", "        elif len(unique_values) > 20:\n", "            try:\n", "                # Try to convert to numbers\n", "                numeric_values = []\n", "                for v in clean_values:\n", "                    try:\n", "                        numeric_values.append(float(v))\n", "                    except (ValueErro<PERSON>, TypeError):\n", "                        break\n", "                \n", "                if len(numeric_values) == len(clean_values):\n", "                    print(f\"      Range: {min(numeric_values)} - {max(numeric_values)}\")\n", "                    print(f\"      Sample values: {clean_values[:5]}\")\n", "                else:\n", "                    print(f\"      Sample values: {clean_values[:5]}\")\n", "            except:\n", "                print(f\"      Sample values: {clean_values[:5]}\")"]}, {"cell_type": "markdown", "id": "ad5c7052", "metadata": {}, "source": ["## 4. Data Visualization and Patterns\n", "\n", "Let's create visualizations to better understand the data patterns and distributions."]}, {"cell_type": "code", "execution_count": 8, "id": "77e19f68", "metadata": {}, "outputs": [{"data": {"image/png": "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***************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["📈 VISUALIZATION SUMMARY\n", "==================================================\n", "Building Types: 4 unique types\n", "Energy Classes: 7 unique classes\n", "Countries: 16 unique countries\n", "Construction Years: 1800 - 2022\n"]}], "source": ["# Create visualizations for key patterns\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('Data Distribution Analysis', fontsize=16, fontweight='bold')\n", "\n", "# 1. Building Types Distribution\n", "building_types = []\n", "for record in data:\n", "    if 'graphMetadata' in record:\n", "        for entity in record['graphMetadata']:\n", "            if 'classType' in entity and 'Building' in entity['classType']:\n", "                if 'propertiesValues' in entity:\n", "                    building_type = entity['propertiesValues'].get('PrimaryTypeOfBuilding')\n", "                    if building_type:\n", "                        building_types.append(building_type)\n", "\n", "building_type_counts = Counter(building_types)\n", "building_labels, building_counts = zip(*building_type_counts.most_common(10))\n", "\n", "axes[0,0].barh(range(len(building_labels)), building_counts, color='lightblue')\n", "axes[0,0].set_yticks(range(len(building_labels)))\n", "axes[0,0].set_yticklabels([label[:20] + '...' if len(label) > 20 else label for label in building_labels])\n", "axes[0,0].set_xlabel('Count')\n", "axes[0,0].set_title('Top Building Types')\n", "axes[0,0].grid(True, alpha=0.3)\n", "\n", "# 2. Energy Efficiency Classes\n", "energy_classes = []\n", "for record in data:\n", "    if 'graphMetadata' in record:\n", "        for entity in record['graphMetadata']:\n", "            if 'classType' in entity and 'Building' in entity['classType']:\n", "                if 'propertiesValues' in entity:\n", "                    energy_class = entity['propertiesValues'].get('EnergyEfficiencyClass')\n", "                    if energy_class:\n", "                        energy_classes.append(energy_class)\n", "\n", "energy_counts = Counter(energy_classes)\n", "energy_labels, energy_vals = zip(*energy_counts.most_common())\n", "\n", "axes[0,1].pie(energy_vals, labels=energy_labels, autopct='%1.1f%%', startangle=90)\n", "axes[0,1].set_title('Energy Efficiency Classes')\n", "\n", "# 3. Countries Distribution\n", "countries = []\n", "for record in data:\n", "    if 'graphMetadata' in record:\n", "        for entity in record['graphMetadata']:\n", "            if 'classType' in entity and 'Address' in entity['classType']:\n", "                if 'propertiesValues' in entity:\n", "                    country = entity['propertiesValues'].get('Country')\n", "                    if country:\n", "                        countries.append(country)\n", "\n", "country_counts = Counter(countries)\n", "country_labels, country_vals = zip(*country_counts.most_common())\n", "\n", "axes[1,0].bar(country_labels, country_vals, color='lightgreen')\n", "axes[1,0].set_xlabel('Country')\n", "axes[1,0].set_ylabel('Count')\n", "axes[1,0].set_title('Geographic Distribution')\n", "axes[1,0].tick_params(axis='x', rotation=45)\n", "axes[1,0].grid(True, alpha=0.3)\n", "\n", "# 4. Construction Years Distribution\n", "construction_years = []\n", "for record in data:\n", "    if 'graphMetadata' in record:\n", "        for entity in record['graphMetadata']:\n", "            if 'classType' in entity and 'Building' in entity['classType']:\n", "                if 'propertiesValues' in entity:\n", "                    year = entity['propertiesValues'].get('ConstructionYear')\n", "                    if year:\n", "                        try:\n", "                            construction_years.append(int(year))\n", "                        except (ValueErro<PERSON>, TypeError):\n", "                            pass\n", "\n", "if construction_years:\n", "    axes[1,1].hist(construction_years, bins=20, color='lightcoral', alpha=0.7, edgecolor='black')\n", "    axes[1,1].set_xlabel('Construction Year')\n", "    axes[1,1].set_ylabel('Frequency')\n", "    axes[1,1].set_title('Construction Year Distribution')\n", "    axes[1,1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(f\"📈 VISUALIZATION SUMMARY\")\n", "print(\"=\" * 50)\n", "print(f\"Building Types: {len(building_type_counts)} unique types\")\n", "print(f\"Energy Classes: {len(energy_counts)} unique classes\")  \n", "print(f\"Countries: {len(country_counts)} unique countries\")\n", "print(f\"Construction Years: {min(construction_years) if construction_years else 'N/A'} - {max(construction_years) if construction_years else 'N/A'}\")"]}, {"cell_type": "markdown", "id": "cf42b60f", "metadata": {}, "source": ["## 5. Relationship and Entity Mapping\n", "\n", "Let's analyze how addresses and buildings are connected through the graph relationships."]}, {"cell_type": "code", "execution_count": 9, "id": "5806011f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🔗 ENTITY RELATIONSHIP ANALYSIS\n", "==================================================\n", "Address → Building mappings: 137\n", "Building → Address mappings: 137\n", "Total entities in lookup: 274\n", "\n", "🔍 RELATIONSHIP CONSISTENCY CHECK:\n", "Consistent relationships: 137\n", "Inconsistent relationships: 0\n", "\n", "📋 SAMPLE RELATIONSHIPS:\n", "\n", "1. Address bf0bf34a-a3a2-4616-a0f7-811a11641cc2 → Building 57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2\n", "   📍 Address: Barthstraße 12, München, Germany\n", "   🏢 Building: RONDO (Office, built 2001)\n", "\n", "2. Address 1b0cf251-2bb1-4a54-a669-3dd89d3d1412 → Building 10d7c8c3-bc2f-4166-97d7-d11f04a39d82\n", "   📍 Address: Friedrichstraße 50-55, Berlin, Germany\n", "   🏢 Building: Checkpoint Charlie (Office, built 1999)\n", "\n", "3. Address b57b570f-8710-434f-b2f3-eaa809241db0 → Building be0f3b3e-c7ed-4178-8cc4-6114ad605936\n", "   📍 Address: Reinhardtstraße 52, Berlin, Germany\n", "   🏢 Building: Waterfalls Berlin (Office, built 2003)\n", "\n", "⚠️  Entities without relationships: 0\n"]}], "source": ["# Analyze entity relationships and mappings\n", "print(\"🔗 ENTITY RELATIONSHIP ANALYSIS\")\n", "print(\"=\" * 50)\n", "\n", "# Create mappings between addresses and buildings\n", "address_to_building = {}\n", "building_to_address = {}\n", "entity_lookup = {}\n", "\n", "for record in data:\n", "    # Extract relationship from graphData\n", "    if 'graphData' in record and record['graphData']:\n", "        graph_content = record['graphData']\n", "        # Find the relationship triple\n", "        for line in graph_content.split('\\n'):\n", "            if 'ibpdi:hasBuilding' in line and 'inst:' in line:\n", "                parts = line.strip().split()\n", "                if len(parts) >= 3:\n", "                    address_id = parts[0].replace('inst:', '').replace('>', '')\n", "                    building_id = parts[2].replace('inst:', '').replace('.', '').replace('>', '')\n", "                    address_to_building[address_id] = building_id\n", "                    building_to_address[building_id] = address_id\n", "    \n", "    # Build entity lookup table\n", "    if 'graphMetadata' in record and record['graphMetadata']:\n", "        for entity in record['graphMetadata']:\n", "            entity_id = entity.get('id')\n", "            if entity_id:\n", "                entity_lookup[entity_id] = entity\n", "\n", "print(f\"Address → Building mappings: {len(address_to_building)}\")\n", "print(f\"Building → Address mappings: {len(building_to_address)}\")\n", "print(f\"Total entities in lookup: {len(entity_lookup)}\")\n", "\n", "# Verify relationship consistency\n", "print(f\"\\n🔍 RELATIONSHIP CONSISTENCY CHECK:\")\n", "consistent_relationships = 0\n", "inconsistent_relationships = 0\n", "\n", "for addr_id, bldg_id in address_to_building.items():\n", "    if bldg_id in building_to_address and building_to_address[bldg_id] == addr_id:\n", "        consistent_relationships += 1\n", "    else:\n", "        inconsistent_relationships += 1\n", "\n", "print(f\"Consistent relationships: {consistent_relationships}\")\n", "print(f\"Inconsistent relationships: {inconsistent_relationships}\")\n", "\n", "# Sample relationship analysis\n", "print(f\"\\n📋 SAMPLE RELATIONSHIPS:\")\n", "sample_count = 0\n", "for addr_id, bldg_id in list(address_to_building.items())[:3]:\n", "    sample_count += 1\n", "    print(f\"\\n{sample_count}. Address {addr_id} → Building {bldg_id}\")\n", "    \n", "    # Get address details\n", "    if addr_id in entity_lookup:\n", "        addr_entity = entity_lookup[addr_id]\n", "        addr_props = addr_entity.get('propertiesValues', {})\n", "        street = addr_props.get('StreetName', 'N/A')\n", "        number = addr_props.get('House<PERSON><PERSON>ber', 'N/A')\n", "        city = addr_props.get('City', 'N/A')\n", "        country = addr_props.get('Country', 'N/A')\n", "        print(f\"   📍 Address: {street} {number}, {city}, {country}\")\n", "    \n", "    # Get building details  \n", "    if bldg_id in entity_lookup:\n", "        bldg_entity = entity_lookup[bldg_id]\n", "        bldg_props = bldg_entity.get('propertiesValues', {})\n", "        name = bldg_props.get('Name', 'N/A')\n", "        building_type = bldg_props.get('PrimaryTypeOfBuilding', 'N/A')\n", "        year = bldg_props.get('ConstructionYear', 'N/A')\n", "        print(f\"   🏢 Building: {name} ({building_type}, built {year})\")\n", "\n", "# Analyze missing relationships\n", "missing_addresses = set(entity_lookup.keys()) - set(address_to_building.keys()) - set(building_to_address.keys())\n", "print(f\"\\n⚠️  Entities without relationships: {len(missing_addresses)}\")\n", "if missing_addresses:\n", "    print(f\"   Sample missing entity IDs: {list(missing_addresses)[:3]}\")"]}, {"cell_type": "markdown", "id": "ef04472e", "metadata": {}, "source": ["## 6. <PERSON><PERSON><PERSON> and <PERSON> Findings\n", "\n", "Based on the comprehensive analysis of the RDF graph data, here are the key insights and findings."]}, {"cell_type": "code", "execution_count": 10, "id": "470822a1", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📋 COMPREHENSIVE DATA UNDERSTANDING SUMMARY\n", "============================================================\n", "\n", "🗃️  DATASET OVERVIEW:\n", "   • Total Records: 137\n", "   • Total Entities: 274 (137 Addresses + 137 Buildings)\n", "   • Relationships: 137 perfect Address ↔ Building mappings\n", "   • Data Completeness: Very high (>90% for most fields)\n", "\n", "🏗️  DATA STRUCTURE INSIGHTS:\n", "   • Format: JSON array with consistent structure\n", "   • Graph Template: Single template for all records\n", "   • Graph Data: RDF/Turtle format with 'ibpdi:hasBuilding' relationships\n", "   • Metadata: Structured entities with properties and values\n", "\n", "📊  ENTITY TYPES & PROPERTIES:\n", "   • Address Entity Properties:\n", "     - StreetName (97.1%), HouseNumber (89.8%), PostalCode (100%)\n", "     - City (99.3%), Country (100%)\n", "   • Building Entity Properties:\n", "     - All properties have >99% completeness\n", "     - PrimaryTypeOfBuilding, Name, BuildingCode, ConstructionYear\n", "     - EnergyEfficiencyClass, PrimaryHeatingType, ParkingSpaces, ValidFrom\n", "\n", "🌍  GEOGRAPHIC & TEMPORAL DISTRIBUTION:\n", "   • Countries: 16 countries, primarily Germany\n", "   • Construction Years: 1800-2022\n", "   • Building Types: Office (majority), Retail, Hotel, Industrial\n", "   • Energy Classes: 7 classes (k=61.3%, C=11.7%, D=10.2%)\n", "\n", "🔍  DATA QUALITY ASSESSMENT:\n", "   ✅ Excellent structural consistency\n", "   ✅ High completeness rates across all fields\n", "   ✅ Perfect relationship mapping (137/137)\n", "   ✅ No orphaned entities\n", "   ✅ Consistent data types and formats\n", "   ✅ Valid RDF/Turtle syntax\n", "\n", "🎯  KEY PATTERNS & RELATIONSHIPS:\n", "   • 1:1 mapping between addresses and buildings\n", "   • Semantic relationship: 'Address hasBuilding Building'\n", "   • Consistent UUID-based entity identification\n", "   • IBPDI ontology namespace usage\n", "   • Structured property-value pairs for each entity\n", "\n", "💡  RECOMMENDATIONS FOR FURTHER ANALYSIS:\n", "   • Geographic clustering analysis by country/city\n", "   • Temporal analysis of construction trends\n", "   • Energy efficiency correlation with building age/type\n", "   • RDF graph querying and semantic reasoning\n", "   • Data integration potential assessment\n", "\n", "============================================================\n", "✨ ANALYSIS COMPLETE - Data is well-structured and analysis-ready! ✨\n"]}], "source": ["# Final Summary and Conclusions\n", "print(\"📋 COMPREHENSIVE DATA UNDERSTANDING SUMMARY\")\n", "print(\"=\" * 60)\n", "\n", "print(\"\\n🗃️  DATASET OVERVIEW:\")\n", "print(f\"   • Total Records: {len(data)}\")\n", "print(f\"   • Total Entities: {len(all_entities)} (137 Addresses + 137 Buildings)\")\n", "print(f\"   • Relationships: {len(address_to_building)} perfect Address ↔ Building mappings\")\n", "print(f\"   • Data Completeness: Very high (>90% for most fields)\")\n", "\n", "print(\"\\n🏗️  DATA STRUCTURE INSIGHTS:\")\n", "print(\"   • Format: JSON array with consistent structure\")\n", "print(\"   • Graph Template: Single template for all records\")  \n", "print(\"   • Graph Data: RDF/Turtle format with 'ibpdi:hasBuilding' relationships\")\n", "print(\"   • Metadata: Structured entities with properties and values\")\n", "\n", "print(\"\\n📊  ENTITY TYPES & PROPERTIES:\")\n", "print(\"   • Address Entity Properties:\")\n", "print(\"     - StreetName (97.1%), HouseNumber (89.8%), PostalCode (100%)\")\n", "print(\"     - City (99.3%), Country (100%)\")\n", "print(\"   • Building Entity Properties:\")  \n", "print(\"     - All properties have >99% completeness\")\n", "print(\"     - PrimaryTypeOfBuilding, Name, BuildingCode, ConstructionYear\")\n", "print(\"     - EnergyEfficiencyClass, PrimaryHeatingType, ParkingSpaces, ValidFrom\")\n", "\n", "print(\"\\n🌍  GEOGRAPHIC & TEMPORAL DISTRIBUTION:\")\n", "print(f\"   • Countries: {len(Counter(countries))} countries, primarily Germany\")\n", "print(f\"   • Construction Years: {min(construction_years) if construction_years else 'N/A'}-{max(construction_years) if construction_years else 'N/A'}\")\n", "print(f\"   • Building Types: Office (majority), Retail, Hotel, Industrial\")\n", "print(f\"   • Energy Classes: 7 classes (k=61.3%, C=11.7%, D=10.2%)\")\n", "\n", "print(\"\\n🔍  DATA QUALITY ASSESSMENT:\")\n", "print(\"   ✅ Excellent structural consistency\")\n", "print(\"   ✅ High completeness rates across all fields\")\n", "print(\"   ✅ Perfect relationship mapping (137/137)\")\n", "print(\"   ✅ No orphaned entities\")\n", "print(\"   ✅ Consistent data types and formats\")\n", "print(\"   ✅ Valid RDF/Turtle syntax\")\n", "\n", "print(\"\\n🎯  KEY PATTERNS & RELATIONSHIPS:\")\n", "print(\"   • 1:1 mapping between addresses and buildings\")\n", "print(\"   • Semantic relationship: 'Address hasBuilding Building'\")\n", "print(\"   • Consistent UUID-based entity identification\")\n", "print(\"   • IBPDI ontology namespace usage\")\n", "print(\"   • Structured property-value pairs for each entity\")\n", "\n", "print(\"\\n💡  RECOMMENDATIONS FOR FURTHER ANALYSIS:\")\n", "print(\"   • Geographic clustering analysis by country/city\")\n", "print(\"   • Temporal analysis of construction trends\")  \n", "print(\"   • Energy efficiency correlation with building age/type\")\n", "print(\"   • RDF graph querying and semantic reasoning\")\n", "print(\"   • Data integration potential assessment\")\n", "\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"✨ ANALYSIS COMPLETE - Data is well-structured and analysis-ready! ✨\")"]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.9"}}, "nbformat": 4, "nbformat_minor": 5}