[{"@id": "https://example.com/52ca00a9-2cbe-4f54-8ea8-7b9f62c29f5d", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000053}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2009}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Distribution Center Flextronics"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/0a2add59-d7d2-4802-ac6d-2bbe4f1a4575", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000135}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2007}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Forum Gliwice"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 823}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/31d6ca19-8a41-4167-8cc0-932923518399", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "AB Rotterdam"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 63}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 3012}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Coolsingel"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/8e69a400-3087-4b3d-93a4-fcf0ba1dafd3"}]}, {"@id": "https://example.com/d036555b-4752-44a8-bad4-b4bdf9c3c6b5", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Berlin"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "106-108"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 10623}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Straße des Juni"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/b663e664-9bcb-4adb-afb0-b3f7118ecec5"}]}, {"@id": "https://example.com/284976c1-a297-4cee-9ef2-5d28e47f54a8", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Milano MI"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Italy"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 13}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20123}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Orefici"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/90e3d68a-313d-401d-82cd-f03587ddda23"}]}, {"@id": "https://example.com/42212432-19cd-4864-9de2-0d3f461f7e07", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": " Praha 4-<PERSON><PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Czechia"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 8}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "140 00"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "St James's Square St. James's"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/7310956d-6d8f-4bd7-af7b-4943aae4c2a4"}]}, {"@id": "https://example.com/5f3672ed-fdea-4db2-b4cf-d06f9327d474", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000127}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2010}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "CTPark Prague North 1"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/fcde3cfb-9806-48b3-99c8-34f08dda3dc7", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 3}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "SW1E 5ER"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Upowa"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/b0d2d212-2722-4e01-a3c2-721dc3eaed93"}]}, {"@id": "https://example.com/96c6d1fb-8146-42fc-bbf1-5ac0f10926c7", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000079}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2006}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Palestra"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 30}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/be3982d9-1086-4de2-a5be-614a95e13330", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000084}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2014}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON> MP2"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/47a26dba-3230-4850-a9a0-f6c0cbc90f1e", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Newbridge"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ireland"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Moorfield"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "unit chaman house"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/7b46d5e9-cb1b-4897-bb20-14c4110629ab"}]}, {"@id": "https://example.com/fe7d490e-2a8c-4329-8c6c-e413e0969bc4", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000033}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1999}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "FLZ – Fracht- und Logistik Zentrum"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Industrial, Distribution Warehouse"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/fcbc08f6-9fb1-4273-be1e-48045f388881", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "W1T 1FB"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Greybowska SA"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/ec3dc6d1-f6ef-4099-909f-ab897b7583ac"}]}, {"@id": "https://example.com/5d003703-9249-494a-9d9e-51c1519178dc", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Oslo"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Norway"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 6}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 161}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Stortingsgata "}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/cd9ae8c1-0380-4ecc-98a3-5510442e3b2c"}]}, {"@id": "https://example.com/42ee10cc-3dfa-496b-b165-8deeb57f63ea", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000036}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2008}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hohe Bleichen 11"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 34}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/8995106d-e483-4266-a3c7-65120a6f3bbe", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000012}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1993}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "E"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Fleethof"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 236}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/68765d34-9b52-4e8f-ade6-f34a9d7ae414", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Cork"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ireland"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "as"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/6f8a060f-0954-411a-aba3-0425fc60288c"}]}, {"@id": "https://example.com/e3784a8d-53fb-44d0-b714-c832770b708f", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Wien"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Austria"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 13}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1010}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Opernving"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/7111ddf7-10b7-4a6b-8ad9-8beccef16f10"}]}, {"@id": "https://example.com/788c3fef-e497-4d68-9111-f5eb2c63e69c", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Barcelona"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Spain"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 8001}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C de Pelai"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/bcb2188b-0ce9-4ec2-8d5c-c9131e302091"}]}, {"@id": "https://example.com/b92396d2-7746-4991-a577-1305fc5a2eb1", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 10}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 81373}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Landaubogen"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/10bd9e8d-dc9f-4cd9-a131-045a9c596498"}]}, {"@id": "https://example.com/3111e590-210c-4593-926e-6700aa0f4514", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Poland"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "44-102"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/0a2add59-d7d2-4802-ac6d-2bbe4f1a4575"}]}, {"@id": "https://example.com/d5ccdae6-a1b9-4d9c-8657-00aa64c5d7a8", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000137}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2003}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "IBC"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 278}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/2ac8ad0b-5f80-4d95-bedc-199479bb3931", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60486}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/06ccb9e5-e34b-4637-aaa4-d5e345cc7d22"}]}, {"@id": "https://example.com/c592f971-2fda-4169-8e89-c7f296a3300f", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000015}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2004}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Prime Parc, Bauteil C1 + Parkhaus"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 283}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/76b5860b-ad34-44ca-909f-15d905d9e8fc", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ra<PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 65479}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Am Prime-Pare"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/d86edecc-2881-4d9e-9372-faa829b541a6"}]}, {"@id": "https://example.com/b441a9c9-c9d1-418d-a5e8-a1a1e8550235", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Düsseldorf"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 3504}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 40474}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Alte FlughafenstraBe"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/46423443-e41a-487d-a07a-c7a9fd5e1ff4"}]}, {"@id": "https://example.com/9bdb9a60-6beb-40da-82cd-d0f8b556ce9d", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000039}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2007}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "F"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Schloss-Arkaden"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1271}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/f1901c05-ebe4-4109-b0b4-540e84878344", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "LS Amsterdam"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 14}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1082}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/e20a7cde-b6df-4ec1-9c21-00cb619c66a6"}]}, {"@id": "https://example.com/bb18b414-c15f-4df6-84c1-53282d7d0d34", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "70 Postřižín-<PERSON><PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Czechia"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 173}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 250}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Vietoria Line"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/654b86d9-c44b-406a-a627-fdfaa29c317d"}]}, {"@id": "https://example.com/ca9bdc08-755d-42fc-b487-741a72c853e2", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 19}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75008}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue de Vienne"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/cbde8030-0835-401a-81bb-239aa0923056"}]}, {"@id": "https://example.com/ff24a9bc-6ab0-40cd-9c66-82a624ef3ea6", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Berlin"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 49}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 10119}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Torstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/a3251601-d4a3-4345-ba97-c7cf47062948"}]}, {"@id": "https://example.com/11f9f028-6ee1-495b-9978-3e1a23d7c3fb", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000026}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1925}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Königstraße 14"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 13}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/2cb71bb8-de4e-4479-8db3-7c9b78801f2c", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Córdoba"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Spain"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 14003}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Calle Conde de Gondomar"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/76eb609f-9a49-4441-9258-a4cb9d63dcd1"}]}, {"@id": "https://example.com/eed9967e-f5b7-4bca-a464-a0638dc60f09", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000057}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2019}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON>, Broekman"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/d63aaff1-e5c0-4fd8-b44d-5816efff4582", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000050}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2009}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 33}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/dd56c51f-8d9b-4a1a-bffc-9c3386c2d868", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "CE Amsterdam"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 595}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1017}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/b4d50434-46d9-4bd1-b7fd-c9336e157401"}]}, {"@id": "https://example.com/d8723f3d-dc77-4539-b854-531c488a12af", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Milano MI"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Italy"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 16}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20121}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Broletto"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/2ce6a300-22c3-4754-a5c4-6d5630632546"}]}, {"@id": "https://example.com/b203841a-89ff-4596-a53c-826c32ab1d9f", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Wien"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Austria"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 6}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1020}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Trabrennstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/87389579-0188-4e4b-af64-10a14fce29f2"}]}, {"@id": "https://example.com/e2ed1add-caca-4511-95d1-5b928f7c3059", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ra<PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 65479}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Stockstrage"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/89afdf02-d465-4814-85ec-9ac00ef4c580"}]}, {"@id": "https://example.com/01bea4e6-fee0-40dc-a829-902d901d0a67", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hamburg"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "1-3"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20355}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/8995106d-e483-4266-a3c7-65120a6f3bbe"}]}, {"@id": "https://example.com/2b5382c5-80cc-4151-a607-38c4a68ed3f7", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000129}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2015}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "8 St James's Square, St. James's,"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 6}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/0b8245dd-551c-45a4-bfc4-473e89ccacb5", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000092}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2019}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Spectrum"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 150}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/8144ad9f-a7f8-48a4-ac31-732aa7ea02b9", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000108}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2007}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Roma 98/a"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/cc4e3285-f6a0-4e9b-a204-4a70b555ab25", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000024}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1922}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "LEOMAX"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 32}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/9be56927-0ba1-4397-b8e2-db496d7528f8", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000081}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2010}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "The St. Botolph Building"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 14}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/d3b37ffb-44f7-4cd3-8da7-22fa34417eb8", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "SC Venlo"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 5928}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Logistiekweg"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/370d688f-fbac-4b18-9444-8ee31e05bb68"}]}, {"@id": "https://example.com/b26ca573-f10b-4568-acfb-63a430ea1e94", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Barcelona"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Spain"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "6,8,"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 8001}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C/ del Pintor Fortuny"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/e9c0ca5b-02e8-4732-8c7d-86632baba8c5"}]}, {"@id": "https://example.com/f978168a-f8c2-44de-a827-ffead32b8cba", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000052}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1998}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "De Resident"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 233}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/ccc75006-4473-44b9-8776-f4bd971be866", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000060}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1996}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Oudergemlaan 214"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 41}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/c00a2afa-7750-402f-a2b0-14fdbf69e046", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "21/V"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 80335}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Bayerstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/a4288287-712a-4c30-86f5-26f4c9b39200"}]}, {"@id": "https://example.com/ad8f9f50-c667-45e6-ac48-ac8d7ed7b29b", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000011}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2002}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "SpreePalais am Dom"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 166}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/1dfae7e5-51a5-4b3d-98dc-1a12529d85ea", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "39-41"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75017}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON> <PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/66c560f4-2f87-40af-9e3c-992d886a1d60"}]}, {"@id": "https://example.com/2f643fd3-9d5b-46f6-81bc-2abd60df713a", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Bert<PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Luxembourg"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 23}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 8070}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue du pults Romain"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/ea27f06d-b7a6-43e5-aee0-8ef57cb35279"}]}, {"@id": "https://example.com/46423443-e41a-487d-a07a-c7a9fd5e1ff4", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000051}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2012}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "CUBES"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 218}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/d54154fb-bd6f-4e1a-b2ef-c314e8ec6d4c", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000047}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2018}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "HighriseOne"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 195}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/e307b8cf-9975-4889-bba5-36524843e3c3", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Warszawa"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Poland"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "00-622"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/d5ccdae6-a1b9-4d9c-8657-00aa64c5d7a8"}]}, {"@id": "https://example.com/b5f9f964-0241-4db9-9ca1-2fc469650560", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000082}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2013}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Margaret Street 33"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 110}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/444f8198-cfe9-4eec-ae72-f690224d8881", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000073}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1972}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "1 Rue des Huissiers"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 294}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/67c98538-4b7c-421c-a9af-6efc3b2b21df", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000122}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2012}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 35-37"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 552}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/5b191f8c-9775-4bd1-9d92-c875100fd773", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000120}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2022}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Oosterdokskade 163"}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/2042576d-5152-49bd-9785-8b8f4ee75d7d", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000119}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1840}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel St. George"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/bac81893-ffc3-44cb-9de1-de34ff78cb73", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "AD Amsterdam"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "59-72"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1012}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/a96f509a-1ce8-4a30-9e24-0144bd202cf2"}]}, {"@id": "https://example.com/2fd1e79c-ba78-4324-90dd-0f19b9fa2a5f", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "101d"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "EC2R 8DN"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Jewry"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/9003555b-57c5-426a-b8e5-31fcdc035e5b"}]}, {"@id": "https://example.com/dd002b44-8118-4469-8cc5-1db5bcf63c0f", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ke<PERSON>terbach"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 65451}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Spreestraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/858414b0-9520-4d68-afac-b856688eb3d7"}]}, {"@id": "https://example.com/c2cb5e24-bff4-4bc9-aaba-4d6de75dce3a", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000086}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2004}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Tesco Distribution Center"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Industrial, Distribution Warehouse"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/649fc234-610d-414b-ba34-70f124825a1f", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 53}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75009}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue de la Victoire"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/d577dc63-6bf1-4c89-8d9b-f4ee302444d0"}]}, {"@id": "https://example.com/aadde3f1-ec26-4962-85b6-7e38dcb4875c", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Bert<PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Luxembourg"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 23}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 8070}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue du pults Romain"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/a451ae80-eb47-4842-a66c-5340406fe0d9"}]}, {"@id": "https://example.com/565f7e3e-50b6-4fcc-b865-d7d53cf1515f", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Bremen"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 31}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 28197}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON> <PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/3c04623f-e668-40e8-81a8-1945d6e577d8"}]}, {"@id": "https://example.com/2e503f44-0591-486c-b333-f326ed980a21", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000085}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2007}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Bema Plaza"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 397}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/6ae41207-b687-4503-8ee6-5e6c14283f21", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 120}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "EC2Y 5ET"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Moor House London Wall"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/4e5596f9-f928-47a8-907f-2538d291e0be"}]}, {"@id": "https://example.com/8fa01b37-3d32-4a26-9aea-0852d3d4c191", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000123}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2006}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rte des Acacias 60"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 827}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/1761d76e-4804-4a9d-adaa-669822e424d0", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": " Madrid"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Spain"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 28012}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/319ebfe7-3d4a-4b71-9216-e65dfb668f81"}]}, {"@id": "https://example.com/89afdf02-d465-4814-85ec-9ac00ef4c580", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000014}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2001}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON> <PERSON>, Bauteil B1-B8"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 270}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/bcb2188b-0ce9-4ec2-8d5c-c9131e302091", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000068}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1936}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/986ee284-f90a-4d68-a0a1-7294fe92de88", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000030}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2009}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Logistikzentrum Rhein-Ruhr"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Industrial, Distribution Warehouse"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/90e3d68a-313d-401d-82cd-f03587ddda23", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000105}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1892}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Orefici 13"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/3d6a0fbc-c197-4bd0-9c26-c7d1c5767f5b", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Saint-Josse-ten-Noode"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Belgium"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 55}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1210}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue du Progrés"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/6edcfd7b-47ed-46f1-9a91-c3e99a082b92"}]}, {"@id": "https://example.com/3ac9d6af-380d-4db7-a3be-5b24400c1edb", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Berlin"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 24}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 10117}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "GeorgenstraBe"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/99120dc1-8ee9-46c3-b465-c7e890895a76"}]}, {"@id": "https://example.com/aa90da7f-a054-4440-987d-b54aedd519e5", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Braunschweig"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 38100}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/9bdb9a60-6beb-40da-82cd-d0f8b556ce9d"}]}, {"@id": "https://example.com/f4881573-ad13-4d33-a0c4-2a9de37a4d4d", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 54}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75009}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/dfa528aa-af9f-48bd-b0a6-dd7320157c8b"}]}, {"@id": "https://example.com/da857556-b790-4f2c-b193-523c29501fe1", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000019}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1977}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Novum"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 24}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/10bd9e8d-dc9f-4cd9-a131-045a9c596498", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000028}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1990}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Alpha-Haus"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 291}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/cbde8030-0835-401a-81bb-239aa0923056", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000101}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1911}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Solstys"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 197}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/e2d82b02-76d0-4ceb-9df8-5c6293a333a6", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000043}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2003}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>sie"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 491}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/7c1f5556-2aa4-420f-a504-dd1b2ed9984a", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Nürnberg"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "32-36"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 90402}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Karolinenstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/c7848807-75bd-4dc2-8bec-9df8c7949fe2"}]}, {"@id": "https://example.com/e9c0ca5b-02e8-4732-8c7d-86632baba8c5", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000065}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1956}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel \"Le Méridien\""}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 23}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/1b9df382-bb91-4587-922b-0db7d83cf54d", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Milano MI"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Italy"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 8}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20124}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Ferrante Aport"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/fdb38a15-d380-49ae-8639-cc5861edc1a3"}]}, {"@id": "https://example.com/6edcfd7b-47ed-46f1-9a91-c3e99a082b92", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000090}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2000}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 405}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/333666e7-9f66-4408-b40d-f16af038ef88", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Warszawa"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Poland"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "00-132"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/de3e19d1-687f-4ffe-83f5-96363f23275d"}]}, {"@id": "https://example.com/763d5362-1d55-4319-8832-f931e89dd3d8", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000037}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2010}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Arnulfstraße 59"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 210}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/93b5c1ab-31c4-40e3-aa9d-5c24b203f420", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Wien"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Austria"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "7/27.06"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1220}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Donau-City trae"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/b0a3f8cc-829e-4fcd-bcbc-1b1db8d55bb0"}]}, {"@id": "https://example.com/b0d2d212-2722-4e01-a3c2-721dc3eaed93", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000130}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1960}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "173 Victoria Line"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/a2e0b5f8-b290-440f-bdfb-4861730dc59a", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000022}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1975}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Westend Sky"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 122}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/8e69a400-3087-4b3d-93a4-fcf0ba1dafd3", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000056}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1954}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Cool 63"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 37}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/0058c837-25b9-4345-9cc1-ec056f456052", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000094}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2009}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "PlusZwei"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 430}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/7b172331-43e3-4351-9675-3067b19aae30", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "34-36"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "W1G 0JE"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Margaret St"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/b5f9f964-0241-4db9-9ca1-2fc469650560"}]}, {"@id": "https://example.com/2e1072c6-e9fe-4f24-9536-248872883738", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ra<PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 65479}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Kelsterbacher Str."}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/c592f971-2fda-4169-8e89-c7f296a3300f"}]}, {"@id": "https://example.com/4c76dfb7-9af2-4a0b-9a1a-5c4f93c48231", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Zürich"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Switzerland"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 29}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 8008}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Utoqual"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/f52ac78d-3bb0-4c2a-9777-39f65ccf1716"}]}, {"@id": "https://example.com/64eeb72f-abac-48a6-bf44-1e9e403bb579", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Warszawa"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Poland"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 28}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "00-847"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Towarowa"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/c159db6c-d0a5-4298-baff-f65a2d735bcc"}]}, {"@id": "https://example.com/58148b74-42e6-4f2e-a8b9-5c82e4bce54e", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Milton Keynes"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "MK17 8EW"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON> treet"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/be3982d9-1086-4de2-a5be-614a95e13330"}]}, {"@id": "https://example.com/6f8a060f-0954-411a-aba3-0425fc60288c", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000117}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2005}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Mahon Point Shopping Center"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/87389579-0188-4e4b-af64-10a14fce29f2", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000093}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2009}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "HochZwei"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 69}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/30d380a3-1509-4e99-af47-01846458182b", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ra<PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 65479}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Stockstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/6ac5af7c-329c-47b1-82c0-e21c10454b38"}]}, {"@id": "https://example.com/aeb649e7-a519-44d8-939f-e7210a2c5cba", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 558}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60549}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Cargo City Sud Gebaude"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/8c5f61ab-40f1-4aac-9d42-f5c3abff7b6f"}]}, {"@id": "https://example.com/de3e19d1-687f-4ffe-83f5-96363f23275d", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000136}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2009}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Grzybowska Park"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 66}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/128ffd41-54ff-4045-b654-54ed4c12b7de", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Poland"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 11}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "96-500"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/c2cb5e24-bff4-4bc9-aaba-4d6de75dce3a"}]}, {"@id": "https://example.com/dfcf1ea1-69bb-416d-a393-1ca5cd58aa5a", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000095}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1993}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Alta Diagonal"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 427}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/a3408b13-da13-44c7-af5b-4353f5491be8", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Issy-les-Moulineaux"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "9-15"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 92130}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue <PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/c82e04a6-352d-4b11-8fa8-1ee6d861973e"}]}, {"@id": "https://example.com/40c031cd-6838-44e2-bd5f-8509fb2ff226", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000128}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2018}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "CTPark Prague North 2"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/d86edecc-2881-4d9e-9372-faa829b541a6", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000016}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2007}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Prime Parc, Bauteil C2"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/21c44521-ea5e-4e8e-97ce-d67fd02eeac4", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000103}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1860}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "23 Opéra"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/a451ae80-eb47-4842-a66c-5340406fe0d9", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000114}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2011}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Vitrum"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 540}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/1106fa42-028e-4f90-b4b9-4490da000d35", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 22}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75001}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue des Pyramides"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/21c44521-ea5e-4e8e-97ce-d67fd02eeac4"}]}, {"@id": "https://example.com/f52ac78d-3bb0-4c2a-9777-39f65ccf1716", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000075}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1900}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Utoquai 29"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/8c5f61ab-40f1-4aac-9d42-f5c3abff7b6f", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000032}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2000}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "E"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "FLZ – Fracht- und Logistik Zentrum"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Industrial, Distribution Warehouse"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/370d688f-fbac-4b18-9444-8ee31e05bb68", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000058}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2020}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "vidaXL Phase 3"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/12e4be74-2fbb-4b4a-b5b7-a9bc6c8e8a1a", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Dublin"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ireland"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 11}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "North"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/a5271b50-54b9-4517-af00-c7c2d163a1f4"}]}, {"@id": "https://example.com/66c560f4-2f87-40af-9e3c-992d886a1d60", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000100}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2009}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel Renaissance Arc de Triomphe"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 38}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/9b6472d3-93ae-4dd2-a2a8-2030afc8ed1a", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ke<PERSON>terbach"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 65451}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Donaustraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/2c5ac2b6-7887-4b38-9203-c94df0e68b67"}]}, {"@id": "https://example.com/b51655df-18bc-4b4f-b8a6-48337c68757d", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000018}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1966}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Lighttower"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 57}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/dfa528aa-af9f-48bd-b0a6-dd7320157c8b", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000097}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2000}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "54 <PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 73}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/b491d9b2-c2e6-419c-a6e6-1ab7c91f1dda", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Barcelona"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Spain"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Alta Diagonal, Av. <PERSON>, 640, 6º A"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Alta Diagonal"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/dfcf1ea1-69bb-416d-a393-1ca5cd58aa5a"}]}, {"@id": "https://example.com/52c8308e-0275-4c03-ac5f-32fd2820bd92", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 4}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 80331}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/cc4e3285-f6a0-4e9b-a204-4a70b555ab25"}]}, {"@id": "https://example.com/7d033169-6efc-4f1a-8128-20e1bdfece9c", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "45 Kozomín"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Czechia"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 277}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Budapest"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/5f3672ed-fdea-4db2-b4cf-d06f9327d474"}]}, {"@id": "https://example.com/df4eccb4-8f5c-4cf5-8365-0e3c228c4ffe", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Neudorf-Weimershof "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Luxembourg"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 38}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1855}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Av. <PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/6358540d-a6cf-4928-a3a9-05f7ed43f408"}]}, {"@id": "https://example.com/075bf17a-11cc-4609-83ed-5b172ae187f2", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000017}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1999}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "A"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Magnusstraße 11"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 149}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/955aafb7-4870-42bd-b447-1fe6ced2e879", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 558}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60549}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Cargo City Sud Gebaude"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/fe7d490e-2a8c-4329-8c6c-e413e0969bc4"}]}, {"@id": "https://example.com/0fd04dd8-da57-4c66-a5b8-ecb693f833f4", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000034}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1966}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Große Elbstraße 14"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/0b3dfe7c-08fd-4139-8fc4-f99d963cd23c", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000132}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1998}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 110}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/7696f6fa-4117-4c41-aa92-3b1a9b52d852", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "126-128"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60314}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>auer Landstrae"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/b51655df-18bc-4b4f-b8a6-48337c68757d"}]}, {"@id": "https://example.com/c8baecf2-ab5f-44c3-b3b9-3251dbb049db", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Poznań"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Poland"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "61-894"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/bb24716d-c0d1-4772-96f7-0a69002d759f"}]}, {"@id": "https://example.com/a3251601-d4a3-4345-ba97-c7cf47062948", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000031}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1996}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "F"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Schönhauser Tor"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 124}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/bb24716d-c0d1-4772-96f7-0a69002d759f", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000134}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2007}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Andersia Tower"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 217}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/10d7c8c3-bc2f-4166-97d7-d11f04a39d82", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000002}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1999}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Checkpoint Charlie"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 207}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/06c10c44-8944-4842-8538-20d5aba3f7bb", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 30}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60325}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ulmenstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/a2e0b5f8-b290-440f-bdfb-4861730dc59a"}]}, {"@id": "https://example.com/65c6d94e-ba3f-4c7e-8c49-942e1712cf1e", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000010}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2003}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 414}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/f77edc3d-5b2c-42bb-b7e9-8efb5c218cb2", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000072}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2007}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "35 Rue de la Gare"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 412}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/7ff07085-d6c9-4e05-8a75-9f609aa16c58", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Düsseldorf"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "18-20"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 40213}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/65c6d94e-ba3f-4c7e-8c49-942e1712cf1e"}]}, {"@id": "https://example.com/9faf0eca-cfff-4933-a8c9-52a2cfc706d7", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "35-37"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60327}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/67c98538-4b7c-421c-a9af-6efc3b2b21df"}]}, {"@id": "https://example.com/fc4c1b2b-349d-4e65-a9dd-4e6ecbfb6037", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Köln"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "2-4"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 50667}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Neumarkt"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/3942f7e3-440b-47b2-9c0a-587c5543c71e"}]}, {"@id": "https://example.com/cccc8903-cbd3-4636-b0ea-992c16c004d3", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000062}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2002}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Business Center Muthgasse"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 228}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/d5b4ed3a-a1b5-4987-a0b1-1804babf924a", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Barcelona"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Spain"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 443}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 8002}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "PI de Catalunya Planta Oficina"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/deeddaa1-108b-4c09-a75d-5d867249b53f"}]}, {"@id": "https://example.com/fb06d8da-e554-4132-b1fd-00462f3ec118", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Neuilly-sur-Seine"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 92200}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue des Hussiers"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/444f8198-cfe9-4eec-ae72-f690224d8881"}]}, {"@id": "https://example.com/e20a7cde-b6df-4ec1-9c21-00cb619c66a6", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000089}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2005}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/60f54207-6c9f-4e75-bf57-6fc7a0cc9f91", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 293}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60326}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Mainzer Landstratle"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/5045a210-29bb-4b4e-b073-3026d291047d"}]}, {"@id": "https://example.com/9003555b-57c5-426a-b8e5-31fcdc035e5b", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000076}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2008}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Old Jewry"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/58929013-f385-45cc-83e6-bbb1b329818f", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000091}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2019}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "The One"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 148}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/cfa4612e-4f03-4e50-a56f-6111375b2ab5", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000040}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2011}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Metropolis Haus"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/66ef33d4-3f94-4a03-8f2e-6d9ec801ae53", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000080}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2007}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Aldermanbury Square"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 18}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/dd4bb631-939a-45c0-a766-19c050c115af", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 3}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "SW1Y 4JU"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "pl. Wiadysta<PERSON> Andersa"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/2b5382c5-80cc-4151-a607-38c4a68ed3f7"}]}, {"@id": "https://example.com/899d7524-f048-4006-aeb8-7fc0410c05b4", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000027}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2003}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 673}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/0b923e2c-257c-4ddd-9f96-2fea85b3ef65", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "WC1B 5HA"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Southampton Row"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/1ba5c96e-d666-4981-8962-b62d195f4eca"}]}, {"@id": "https://example.com/c237e5e3-9476-410b-b793-321a7f912fa3", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 120}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75008}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue du Faubourg Saint-Honoré"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/975306b0-55ed-4bec-8d1d-eb7403a01369"}]}, {"@id": "https://example.com/02a175f3-9042-44f7-bb2f-a3b9c5997bb8", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "141)"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 81671}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rosenheimer Straßeaße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/d54154fb-bd6f-4e1a-b2ef-c314e8ec6d4c"}]}, {"@id": "https://example.com/2df597b6-5476-42ef-8aad-7796870ca628", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000083}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2013}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Atria One & Two"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/a7168c52-d954-4b0d-bc32-aed52b494de4", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 197}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "SE1 8JZ"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Palestra House Blackfriars Ré"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/96c6d1fb-8146-42fc-bbf1-5ac0f10926c7"}]}, {"@id": "https://example.com/a4288287-712a-4c30-86f5-26f4c9b39200", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000025}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1975}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "A"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Bayerstraße 21/V"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 24}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/858414b0-9520-4d68-afac-b856688eb3d7", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000049}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2020}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Logistikzentrum FFM-Airport, 2. BA"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/be0f3b3e-c7ed-4178-8cc4-6114ad605936", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000003}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2003}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Waterfalls Berlin"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 71}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/5ecf3188-9d58-4652-9d19-1d3794f95420", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "10/12"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75008}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue du Général Foy"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/0a8cbe14-3d18-4c6c-a778-abade5b4b8b1"}]}, {"@id": "https://example.com/b9cca905-ab1c-4958-b3ed-bc37857afa44", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hamburg"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "29-32"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20354}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Dammtorstrafe"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/cfa4612e-4f03-4e50-a56f-6111375b2ab5"}]}, {"@id": "https://example.com/a06ce1f9-d666-48fa-b693-b3beefd98ddd", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 77}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 80339}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/e2d82b02-76d0-4ceb-9df8-5c6293a333a6"}]}, {"@id": "https://example.com/ec3dc6d1-f6ef-4099-909f-ab897b7583ac", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000131}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2017}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "One Rathbone Square"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/a01e6e44-48b0-41c4-aa7f-2fa3298e0f4c", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000029}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2000}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "F"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Lindner Hotel"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/d577dc63-6bf1-4c89-8d9b-f4ee302444d0", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000069}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1998}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Opéra-Victoire"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 227}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/0a8cbe14-3d18-4c6c-a778-abade5b4b8b1", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000099}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2018}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 79}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/9d82f641-36f6-465c-9b5b-947abd3ed1d3", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hamburg"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 14}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 22767}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "GroBe Elostraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/0fd04dd8-da57-4c66-a5b8-ecb693f833f4"}]}, {"@id": "https://example.com/fdb38a15-d380-49ae-8639-cc5861edc1a3", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000104}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2000}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Palazzo Aporti"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 241}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/b2912904-1a1e-4f3e-8a85-cc98d196c385", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000096}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2010}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "EQWATER"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 220}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/1cbd2116-7b88-4f4b-9c35-c6e947928c70", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "NW Venlo"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 8}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 5928}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Doctor <PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/eed9967e-f5b7-4bca-a464-a0638dc60f09"}]}, {"@id": "https://example.com/1f1024af-c0f0-44f6-881b-77dae01bbd6a", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000004}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2003}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "AIR CARGO Center"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Industrial, Distribution Warehouse"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/88d9f843-a782-41db-a5c6-e909410cc4d6", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 80336}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Goethestrage"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/5154cc9b-6a58-4bb4-a015-7bad385f7c0c"}]}, {"@id": "https://example.com/2dfe9323-7ae5-47ee-bc08-413519ce45b6", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Milano MI"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Italy"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "18,"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20159}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via <PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/adbd1c9e-49b4-4f4d-8155-6329f72b6b6d"}]}, {"@id": "https://example.com/975306b0-55ed-4bec-8d1d-eb7403a01369", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000070}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2018}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "120 Rue du Faubourg Saint-Honoré"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 26}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/bcd00c07-4e85-428e-9d9c-0cba6b3f2a1c", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Magenta MI"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Italy"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "98/2"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20013}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Roma"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/8144ad9f-a7f8-48a4-ac31-732aa7ea02b9"}]}, {"@id": "https://example.com/726681f4-2e71-4cd5-9498-97bc5627ca66", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Karlsruhe"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 26}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 76133}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ka<PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/a8fa7d8c-dd49-4d11-9379-3d2d6ab2d67e"}]}, {"@id": "https://example.com/7b46d5e9-cb1b-4897-bb20-14c4110629ab", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000116}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2006}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Whitewater"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1701}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/73a9aab9-5599-4056-b3cd-40ab314eb13b", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Köln"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "6-8"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 50668}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "An den Dominikanern"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/a01e6e44-48b0-41c4-aa7f-2fa3298e0f4c"}]}, {"@id": "https://example.com/1ba5c96e-d666-4981-8962-b62d195f4eca", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000078}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1900}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "One Southampton Row"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 6}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/4e5596f9-f928-47a8-907f-2538d291e0be", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000077}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2005}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Moor House"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 15}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/832cb406-5337-4132-81b6-74298751d028", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000071}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1878}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Le Centorial"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 267}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/8036cf7f-0f2e-4e72-9585-2a0213cc4433", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Stuttgart"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 14}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 70173}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "KonigstraBe"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/11f9f028-6ee1-495b-9978-3e1a23d7c3fb"}]}, {"@id": "https://example.com/1ccb4dde-ccc2-4267-aa90-d682090fe9ab", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000063}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1904}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Kaufhaus Gerngross"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/97b5f5d4-eebf-4874-b9f5-83bbf9f106fa", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000107}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2010}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Logistics Centre A/C"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Industrial, Distribution Warehouse"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/654b86d9-c44b-406a-a627-fdfaa29c317d", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000125}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2006}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Tesco Distribution Center"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/99f94589-009f-4322-b708-2ef93c4431c6", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Pontenure PC"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Italy"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 29010}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via samu Amada"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/a02777cc-ae87-43f5-8a32-87d522d24d00"}]}, {"@id": "https://example.com/5baac072-c349-4548-befb-978d63ebc8e8", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Carouge"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Switzerland"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1227}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rte des Acacias"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/8fa01b37-3d32-4a26-9aea-0852d3d4c191"}]}, {"@id": "https://example.com/534b5c05-f162-4d18-bc0e-a68ae9ac21e2", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 3}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60528}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Herriotstrafe"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/899d7524-f048-4006-aeb8-7fc0410c05b4"}]}, {"@id": "https://example.com/6358540d-a6cf-4928-a3a9-05f7ed43f408", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000113}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2002}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "38 Av. <PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 322}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/635d067f-ebd5-4383-98fc-ea28ba0f2f14", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "139 00 Praha 4-<PERSON><PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Czechia"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "140 00"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rathbone Sq"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/8e8d2928-9d12-4b2a-8e7e-5136c6dd736a"}]}, {"@id": "https://example.com/7dfc816c-cce4-4dd7-9a89-c2c0a04731f1", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Berlin"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 10178}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Anna-<PERSON><PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/ad8f9f50-c667-45e6-ac48-ac8d7ed7b29b"}]}, {"@id": "https://example.com/d965425e-fb4e-4c22-98c2-477c20f5bac1", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "45 Kozomín"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Czechia"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 6}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 277}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Stortingsgata"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/40c031cd-6838-44e2-bd5f-8509fb2ff226"}]}, {"@id": "https://example.com/7643f508-bea8-49c4-b403-ff72b62921bb", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Wien"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Austria"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1982}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1190}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Knoten NuBdor Tragwerk"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/cccc8903-cbd3-4636-b0ea-992c16c004d3"}]}, {"@id": "https://example.com/08ab822a-72e3-4967-8eb2-acffafe469e9", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 4}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75002}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue du septembre"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/832cb406-5337-4132-81b6-74298751d028"}]}, {"@id": "https://example.com/a72447d7-8f89-416a-a7df-fb025553b122", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Wrocław"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Poland"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "50-265"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Generata Jévefa <PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/2e503f44-0591-486c-b333-f326ed980a21"}]}, {"@id": "https://example.com/a8fa7d8c-dd49-4d11-9379-3d2d6ab2d67e", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000088}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2005}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON> Tor Karlsruhe"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 846}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/c9112f6e-7cc0-4164-bf33-dc5cb6850981", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Brussel"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Belgium"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 12}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Bischoffsheimlaan"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/0b8245dd-551c-45a4-bfc4-473e89ccacb5"}]}, {"@id": "https://example.com/7111ddf7-10b7-4a6b-8ad9-8beccef16f10", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000061}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2019}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel \"Le Méridien\""}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/c7848807-75bd-4dc2-8bec-9df8c7949fe2", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000006}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2003}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 107}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/a96f509a-1ce8-4a30-9e24-0144bd202cf2", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000055}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2016}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel NH Collection Amsterdam"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 26}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/3c04623f-e668-40e8-81a8-1945d6e577d8", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000035}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2009}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Logistikzentrum Bremen"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Industrial, Distribution Warehouse"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/deeddaa1-108b-4c09-a75d-5d867249b53f", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000064}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1998}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "El Triangle"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 293}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/0d35fb07-63a9-4280-a4b2-cb808f0211e6", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Berlin"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 54}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 10117}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Dorotheenstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/d087f4c1-344d-401b-aff2-62dd21d74c68"}]}, {"@id": "https://example.com/fb1f443f-4478-4044-a8e7-d551d75802d1", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000102}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1800}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "33 Rue La Fayette"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 301}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/4620ead9-e050-4d4c-9b7b-d8bff52936b8", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000048}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2006}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Das Schloss Shoppingcenter"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 580}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/c58327a2-b70f-4808-9be6-f3f61c45a367", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Köln"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 11}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 50672}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Magnusstrafe"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/075bf17a-11cc-4609-83ed-5b172ae187f2"}]}, {"@id": "https://example.com/04baa1d3-3007-41e6-ba27-1b124e96706c", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON>"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 22}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 41468}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/986ee284-f90a-4d68-a0a1-7294fe92de88"}]}, {"@id": "https://example.com/4ee0b1b7-86d5-4e77-a9a9-93259cfef09c", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Wien"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Austria"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "42/48"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1070}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Gemngross (Modepalast Concept Store Mariahilfer Straßeaße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/1ccb4dde-ccc2-4267-aa90-d682090fe9ab"}]}, {"@id": "https://example.com/77924ad2-527e-4745-9c45-ce51e1b164af", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": " Castel San Giovanni PC"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Italy"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "2a"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 29015}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Dogana Po"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/f4c3b7c3-eb91-44f3-bce1-ec2903ea58df"}]}, {"@id": "https://example.com/2ce12f63-fcc1-4ef9-bd96-01fe849c6896", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Berlin"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 34}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 12163}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Einkaufszentrum Das Schloss Schlostrae"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/4620ead9-e050-4d4c-9b7b-d8bff52936b8"}]}, {"@id": "https://example.com/d3954c67-c035-4b3e-96bc-29515c31b7f5", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000009}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1991}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Sunyard"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 287}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/a02777cc-ae87-43f5-8a32-87d522d24d00", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000109}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2011}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Logistics Centre B"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Industrial, Distribution Warehouse"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/db63f613-1f33-43e4-aeb0-b1e41c5bbb5d", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Edinburgh "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 144}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "EH3 8EX"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Atria One Morrison St"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/2df597b6-5476-42ef-8aad-7796870ca628"}]}, {"@id": "https://example.com/7d2c59cf-1cae-4f50-b137-ac79103499da", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hamburg"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 84}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20354}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Never Wall"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/d63aaff1-e5c0-4fd8-b44d-5816efff4582"}]}, {"@id": "https://example.com/1b0cf251-2bb1-4a54-a669-3dd89d3d1412", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Berlin"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "50-55"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 10117}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Friedrichstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/10d7c8c3-bc2f-4166-97d7-d11f04a39d82"}]}, {"@id": "https://example.com/7fe74459-5abc-42f4-99c4-d51f6eb05c97", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 571}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60549}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Cargo City Sud Gebiude"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/1f1024af-c0f0-44f6-881b-77dae01bbd6a"}]}, {"@id": "https://example.com/d772cb79-74f8-42e1-b4a1-0998b73fa0d0", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "DL Amsterdam"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 163}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1011}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Oosterdokskade"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/5b191f8c-9775-4bd1-9d92-c875100fd773"}]}, {"@id": "https://example.com/a5271b50-54b9-4517-af00-c7c2d163a1f4", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000118}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2018}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Clayton Hotel Charlemont"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/65263dfa-4542-49bd-893d-e3df9c253592", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 33}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75009}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue La Fayette"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/fb1f443f-4478-4044-a8e7-d551d75802d1"}]}, {"@id": "https://example.com/8342681e-bfbb-4315-8798-908dc9b5f2e9", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Bruxelles"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Belgium"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 107}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1040}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue dela Lol"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/58929013-f385-45cc-83e6-bbb1b329818f"}]}, {"@id": "https://example.com/5b7a76cf-3c73-4553-9c30-5d3320084283", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000106}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2005}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Logistic Park Castel San Giovanni"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Industrial, Distribution Warehouse"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/6ac5af7c-329c-47b1-82c0-e21c10454b38", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000013}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2000}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Prime Parc, Bauteil A1-A5"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/2ce6a300-22c3-4754-a5c4-6d5630632546", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000112}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1871}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Broletto 16"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 30}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/b57b570f-8710-434f-b2f3-eaa809241db0", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Berlin"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 52}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 10117}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Reinhardtstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/be0f3b3e-c7ed-4178-8cc4-6114ad605936"}]}, {"@id": "https://example.com/bf0bf34a-a3a2-4616-a0f7-811a11641cc2", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 12}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 80339}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Barthstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2"}]}, {"@id": "https://example.com/4f72a0ac-ef2f-4e33-aa1b-c34572988b6a", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000020}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1972}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Isenburg-Zentrum"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1530}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/ef0b2a11-9e96-4426-bf4d-8b069bc5db54", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 9}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60311}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Grofe Gallusstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/da857556-b790-4f2c-b193-523c29501fe1"}]}, {"@id": "https://example.com/ad1c0f03-9827-45dd-a93a-2a0732e0af2f", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 138}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "EC3A 7AW"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Houndsditch"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/9be56927-0ba1-4397-b8e2-db496d7528f8"}]}, {"@id": "https://example.com/c159db6c-d0a5-4298-baff-f65a2d735bcc", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000087}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2019}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Generation Park Z"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 110}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/0d2d08d8-62da-415d-83f3-d6796810c147", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hamburg"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 11}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 20354}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hobe Bleichen"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/42ee10cc-3dfa-496b-b165-8deeb57f63ea"}]}, {"@id": "https://example.com/7310956d-6d8f-4bd7-af7b-4943aae4c2a4", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000124}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2008}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Gemini"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 484}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/018ac746-2503-4f2e-b681-8cb37d2f0e29", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurt am Main"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "172-190"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 60327}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Mainzer Landstraße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/1892ebb9-9532-4dee-bddc-a7044da700a2"}]}, {"@id": "https://example.com/57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000001}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2001}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "RONDO"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 324}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/1892ebb9-9532-4dee-bddc-a7044da700a2", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000005}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2003}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "E"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Atrium Plaza"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 151}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/adbd1c9e-49b4-4f4d-8155-6329f72b6b6d", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000111}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2010}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "MAC567"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 287}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/06ccb9e5-e34b-4637-aaa4-d5e345cc7d22", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000021}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1986}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "E"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Poseidon"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 432}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/8e8d2928-9d12-4b2a-8e7e-5136c6dd736a", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000126}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2012}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "City Green Court"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 236}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/b663e664-9bcb-4adb-afb0-b3f7118ecec5", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000008}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2005}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "D"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Tiergarten Tower"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 157}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/5154cc9b-6a58-4bb4-a015-7bad385f7c0c", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000044}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2002}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel Le Méridien"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 72}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/ea27f06d-b7a6-43e5-aee0-8ef57cb35279", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000115}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2005}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Emporium"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 876}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/99120dc1-8ee9-46c3-b465-c7e890895a76", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000038}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2002}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "E"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 201}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/2d38d186-ea79-45de-bd78-6bd20df18893", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Budapest"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hungary"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 7}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1054}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Polna"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/0b3dfe7c-08fd-4139-8fc4-f99d963cd23c"}]}, {"@id": "https://example.com/2c5ac2b6-7887-4b38-9203-c94df0e68b67", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000046}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2017}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "A"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Mönchhof Frankfurt Airport"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/b0a3f8cc-829e-4fcd-bcbc-1b1db8d55bb0", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000121}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2013}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "DC Tower"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 414}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/76eb609f-9a49-4441-9258-a4cb9d63dcd1", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000067}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1989}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Calle Conde de Gondomar"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/34fb1c83-74f2-4bc6-a77a-fabee29812f9", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Neu-Isenburg"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "168-176"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 63263}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Frankfurter Straßeaße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/4f72a0ac-ef2f-4e33-aa1b-c34572988b6a"}]}, {"@id": "https://example.com/1830a091-367f-434a-b2eb-4e495c16d3ff", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Stuttgart"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 70173}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Thouretstrae"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/57cd12c2-f87c-48cf-be65-66574c335774"}]}, {"@id": "https://example.com/a9ebd1a5-73a4-4e41-bf62-81299f25b959", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Helsinki"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Finland"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "13,"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 120}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/2042576d-5152-49bd-9785-8b8f4ee75d7d"}]}, {"@id": "https://example.com/86b89036-0cca-4c25-9aa2-7a0aa670f9b0", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Issy-les-Moulineaux"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 26}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 92130}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue He<PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/b2912904-1a1e-4f3e-8a85-cc98d196c385"}]}, {"@id": "https://example.com/fd3e3af3-a573-4a90-8910-1658cbf7be5d", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "$9"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 80636}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON><PERSON>stra<PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/763d5362-1d55-4319-8832-f931e89dd3d8"}]}, {"@id": "https://example.com/b4d50434-46d9-4bd1-b7fd-c9336e157401", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": **********}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1931}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "The Bank"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 110}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/e64f2098-e585-4b4d-a69f-383d7eb6d1cd", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": **********}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2020}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rijnstraat 192"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 81}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/5045a210-29bb-4b4e-b073-3026d291047d", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000023}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1992}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Mainzer Landstraße 293"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 342}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/ae3ccd79-1850-4ef2-b9ef-fab0b13bf95b", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Etterbeek"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Belgium"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 214}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1040}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Ousergemlaan"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/ccc75006-4473-44b9-8776-f4bd971be866"}]}, {"@id": "https://example.com/319ebfe7-3d4a-4b71-9216-e65dfb668f81", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000066}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1900}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON><PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/f4c3b7c3-eb91-44f3-bce1-ec2903ea58df", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000110}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2011}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Dogana Po 2a"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 0}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/8c8e19d6-5c88-49f1-93a1-d16c78b942cf", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000045}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2004}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "F"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Sofitel Munich Bayerpost"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 70}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Hotel"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/c82e04a6-352d-4b11-8fa8-1ee6d861973e", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000098}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2000}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Central Park"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 310}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/d087f4c1-344d-401b-aff2-62dd21d74c68", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000041}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2002}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "E"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 96}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/bab25ed5-5a81-44c9-83d6-eaabcb196b73", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Castel San Giovanni PC"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Italy"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "2a"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 29015}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Via Dogana Po"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/5b7a76cf-3c73-4553-9c30-5d3320084283"}]}, {"@id": "https://example.com/65e65ff0-7dcd-4fdb-b4be-b411917bf644", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "GA Oirlo"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 10}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 5807}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Nobelstraat"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/52ca00a9-2cbe-4f54-8ea8-7b9f62c29f5d"}]}, {"@id": "https://example.com/77f07fbc-e2c3-438e-b72a-ce4bb3972a5d", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 12}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 80335}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Bayerstrae"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/8c8e19d6-5c88-49f1-93a1-d16c78b942cf"}]}, {"@id": "https://example.com/16521686-eb85-4515-86f4-a6bac3c82392", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Lyon"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 35}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 69007}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Now Coworking Rue de Marseille"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/0fb819dd-8215-4224-be62-f8bc93aab237"}]}, {"@id": "https://example.com/fc6678d2-3ac4-4353-87ae-4dadb8c02d15", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "San Nazzaro PC"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Italy"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 29010}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/97b5f5d4-eebf-4874-b9f5-83bbf9f106fa"}]}, {"@id": "https://example.com/24a9dee6-6646-460d-a75b-a05d2586ae47", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Wien"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Austria"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1020}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "<PERSON>"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/0058c837-25b9-4345-9cc1-ec056f456052"}]}, {"@id": "https://example.com/38c81806-5b93-4038-933b-7c6987835b98", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Paris"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "France"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 35}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 75019}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Rue dela Gare"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/f77edc3d-5b2c-42bb-b7e9-8efb5c218cb2"}]}, {"@id": "https://example.com/638051f6-99d2-4784-8907-c9488441ab6a", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "München"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Germany"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "58-68"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 81541}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "St.<PERSON> Straßeaße"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/d3954c67-c035-4b3e-96bc-29515c31b7f5"}]}, {"@id": "https://example.com/57cd12c2-f87c-48cf-be65-66574c335774", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000007}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2002}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "B"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "s´Zentrum"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 87}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "District heating"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/afbeaae2-a451-4f0a-a7d2-615f48f3b2a6", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "London "}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "UK"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 5}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "EC2V 7BP"}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Aldermanbury Barbican"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/66ef33d4-3f94-4a03-8f2e-6d9ec801ae53"}]}, {"@id": "https://example.com/cd9ae8c1-0380-4ecc-98a3-5510442e3b2c", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000133}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2004}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Stortingsgata 6"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 22}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/3942f7e3-440b-47b2-9c0a-587c5543c71e", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000042}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2013}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "C"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Neumarkt Galerie"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 141}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Retail"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}, {"@id": "https://example.com/009cb9c7-07d7-48fd-a539-672fbe6f2652", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "HS Amsterdam"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/HouseNumber": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 192}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1079}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Aijstraat"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/e64f2098-e585-4b4d-a69f-383d7eb6d1cd"}]}, {"@id": "https://example.com/7893c76e-eefd-4bd4-8ada-e7982d37508f", "@type": ["https://ibpdi.datacat.org/class/Address"], "https://example.com/property/City": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "XP Den Haag"}], "https://example.com/property/Country": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Netherlands"}], "https://example.com/property/PostalCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 2515}], "https://example.com/property/StreetName": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "armassuspleinS"}], "https://ibpdi.datacat.org/class/hasBuilding": [{"@id": "https://example.com/f978168a-f8c2-44de-a827-ffead32b8cba"}]}, {"@id": "https://example.com/0fb819dd-8215-4224-be62-f8bc93aab237", "@type": ["https://ibpdi.datacat.org/class/Building"], "https://example.com/property/BuildingCode": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1000000074}], "https://example.com/property/ConstructionYear": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 1932}], "https://example.com/property/EnergyEfficiencyClass": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "k"}], "https://example.com/property/Name": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "New Deal"}], "https://example.com/property/ParkingSpaces": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 165}], "https://example.com/property/PrimaryHeatingType": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Natural gas"}], "https://example.com/property/PrimaryTypeOfBuilding": [{"@type": "http://www.w3.org/2001/XMLSchema#string", "@value": "Office"}], "https://example.com/property/ValidFrom": [{"@type": "http://www.w3.org/2001/XMLSchema#integer", "@value": 44926}]}]