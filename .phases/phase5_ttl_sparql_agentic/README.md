# Agentic TTL Question-Answering System

This directory contains a completely rewritten, agentic approach to TTL/RDF question-answering using PydanticAI. Unlike the previous rigid pipeline approach, this system allows the AI agent to dynamically decide which tools to use, how many queries to execute, and how to iteratively refine its approach based on intermediate results.

## 🔄 Key Differences from Previous Version

### Previous Approach (Rigid Pipeline)

- **Fixed pipeline**: Question → SPARQL Generation → Execution → Response
- **Single query**: Always generated exactly one query per question
- **No iteration**: No ability to refine queries based on results
- **Limited strategy**: Used the same approach for all question types
- **No tool visibility**: Users couldn't see the internal process

### New Agentic Approach

- **Dynamic strategy**: Agent plans its approach based on question complexity
- **Multi-query capability**: Can generate and execute multiple queries as needed
- **Iterative refinement**: Can analyze results and generate follow-up queries
- **Tool transparency**: All tool calls are displayed to the user
- **Adaptive reasoning**: Different strategies for different question types

## 🧠 Agent Architecture

### Core Agent

The system uses a single intelligent agent (`ttl_qa_agent`) that has access to multiple tools and can reason about which ones to use and when.

### Available Tools

1. **`develop_strategy`**: Plans the overall approach

   - Analyzes question complexity
   - Determines expected number of queries
   - Outlines step-by-step methodology

2. **`generate_sparql_query`**: Creates SPARQL queries

   - Understands TTL structure from analysis
   - Generates syntactically correct queries
   - Validates queries before execution
   - Adapts query style to question type

3. **`execute_sparql_query`**: Runs queries against the graph

   - Executes SPARQL queries safely
   - Returns structured results with metadata
   - Handles errors gracefully

4. **`analyze_query_results`**: Extracts insights from results
   - Analyzes query outcomes
   - Identifies patterns and insights
   - Determines if additional queries are needed

## 🎯 Agent Strategies

The agent automatically selects different strategies based on question characteristics:

### Count-Based Analysis

**Trigger**: Questions containing "how many", "count", "number"

- **Approach**: Direct counting queries
- **Steps**: Generate COUNT query → Execute → Interpret results
- **Expected Queries**: 1

### Exploratory Data Retrieval

**Trigger**: Questions with "what", "which", "list", "show"

- **Approach**: Progressive exploration
- **Steps**: Initial query → Analyze → Follow-up if needed → Synthesize
- **Expected Queries**: 1-2

### Comparative Analysis

**Trigger**: Questions with "compare", "difference", "versus"

- **Approach**: Multi-faceted comparison
- **Steps**: Generate comparative queries → Execute each → Analyze differences
- **Expected Queries**: 2-3

### General Inquiry

**Trigger**: Other question types

- **Approach**: Flexible multi-step analysis
- **Steps**: Broad query → Identify follow-ups → Targeted queries → Synthesize
- **Expected Queries**: 2+

## 🛠️ Tool Call Visualization

The system displays all tool calls made by the agent, including:

- **Tool name** and **arguments** passed
- **Query generation** with syntax highlighting
- **Execution results** with formatted tables
- **Strategy decisions** with reasoning
- **Analysis insights** with confidence levels

## 🚀 Usage Examples

### Simple Count Query

```
User: "How many buildings are there?"

Agent Process:
1. 🎯 develop_strategy → Count-based analysis
2. 🔍 generate_sparql_query → COUNT query for buildings
3. 📊 execute_sparql_query → Returns count
4. 🎯 Final Answer → "There are X buildings in the dataset"
```

### Complex Multi-Step Query

```
User: "Compare residential and commercial buildings"

Agent Process:
1. 🎯 develop_strategy → Comparative analysis
2. 🔍 generate_sparql_query → Query for residential buildings
3. 📊 execute_sparql_query → Get residential data
4. 💡 analyze_query_results → Analyze residential patterns
5. 🔍 generate_sparql_query → Query for commercial buildings
6. 📊 execute_sparql_query → Get commercial data
7. 💡 analyze_query_results → Analyze commercial patterns
8. 🎯 Final Answer → Comprehensive comparison with insights
```

## 💻 Technical Implementation

### Data Models

- **`FinalAnswer`**: Structured final response with methodology
- **`AgentStrategy`**: Strategic planning information
- **`SPARQLQuery`**: Query with validation and metadata
- **`QueryResult`**: Execution results with performance data
- **`AnalysisInsight`**: Extracted insights with confidence

### Error Handling

- **Query validation**: Syntax checking before execution
- **Graceful failures**: Informative error messages
- **Retry logic**: Uses `ModelRetry` for recoverable errors

### Performance Monitoring

- **Token usage** tracking
- **Execution times** for queries
- **Request counts** and costs

## 🎨 User Interface Features

### Beautiful CLI

- **Rich formatting** with colors and panels
- **Progress indicators** during processing
- **Syntax highlighting** for SPARQL queries
- **Structured tables** for results

### Interactive Commands

- `load`/`reload` - Load new TTL file
- `status`/`info` - Show system status
- `help`/`?` - Display help information
- `quit`/`exit`/`q` - Exit application

## 🔧 Configuration

### Model Configuration

Uses Anthropic's Claude 3.5 Sonnet via OpenRouter:

```python
model = OpenAIModel(
    'anthropic/claude-3-5-sonnet',
    provider=OpenAIProvider(
        base_url='https://openrouter.ai/api/v1',
        api_key=OPENROUTER_API_KEY
    )
)
```

### Environment Variables Required

```bash
OR_API_KEY=your_openrouter_api_key
# or
OPENROUTER_API_KEY=your_openrouter_api_key
```

## 🏃 Running the System

1. **Install dependencies**:

   ```bash
   pip install -r requirements_phase.txt
   ```

2. **Set environment variables**:

   ```bash
   export OR_API_KEY=your_openrouter_api_key
   ```

3. **Run the application**:

   ```bash
   python agentic_ttl_qa_cli.py
   ```

4. **Load TTL file** (default provided)

5. **Ask questions** in natural language!

## 🔍 Example Questions

### Basic Queries

- "How many buildings are there?"
- "What cities are in the data?"
- "Show me buildings with parking"

### Advanced Queries

- "Compare energy efficiency ratings across building types"
- "What's the distribution of construction years?"
- "Find patterns in address types by city"

## 🎯 Benefits of Agentic Approach

1. **Flexibility**: Adapts to question complexity automatically
2. **Transparency**: Users see the reasoning process
3. **Accuracy**: Can verify and refine queries based on results
4. **Efficiency**: Uses only the queries needed for each question
5. **Extensibility**: Easy to add new tools and capabilities
6. **Learning**: Can improve strategies based on outcomes

This agentic approach transforms the TTL Q&A system from a rigid pipeline into an intelligent, adaptive assistant that can handle complex analytical questions with human-like reasoning and transparency.
