"""
Ontology-Guideline Merger
========================

This notebook automatically merges RDF ontology files with guideline JSON files
to create enriched ontologies with detailed property definitions.

Input:
- ontology.ttl: Base ontology with classes and object properties
- guideline.json: Detailed property definitions with metadata

Output:
- enriched_ontology.ttl: Merged ontology with data properties and restrictions
"""

import json
import re
from pathlib import Path
from typing import Dict, List, Optional, Any, Set
from urllib.parse import urlparse

# RDF/OWL libraries
from rdflib import Graph, Namespace, URIRef, Literal, BNode
from rdflib.namespace import RDF, RDFS, OWL, XSD

print("✓ Libraries imported successfully")

# Namespace definitions
IBPDI = Namespace("https://ibpdi.datacat.org/class/")
IBPDI_PROP = Namespace("https://ibpdi.datacat.org/property/")
SCHEMA = Namespace("http://schema.org/")
DCTERMS = Namespace("http://purl.org/dc/terms/")

# File paths configuration
BASE_PATH = Path(r"c:\Users\<USER>\Documents\BA\.phases\phase7_tripplestore_eval\assets")
ontology_file = BASE_PATH / "ontology.ttl"
guideline_file = BASE_PATH / "guideline.json" 
output_ontology_file = BASE_PATH / "enriched_ontology.ttl"

print(f"Input files:")
print(f"  Ontology: {ontology_file}")
print(f"  Guideline: {guideline_file}")
print(f"Output file: {output_ontology_file}")

class DataTypeMapper:
    """Maps guideline storage types to XSD data types"""
    
    STORAGE_TYPE_MAP = {
        0: XSD.string,      # Text/String
        1: XSD.integer,     # Integer
        2: XSD.decimal,     # Decimal/Float
        3: XSD.boolean,     # Boolean
        4: XSD.decimal,     # Numeric (default to decimal)
        5: XSD.dateTime,    # DateTime
        6: XSD.date,        # Date
        7: XSD.time,        # Time
        8: XSD.anyURI,      # URI/URL
        9: XSD.string,      # Enumeration (as string)
    }
    
    @classmethod
    def get_xsd_type(cls, storage_type: int) -> URIRef:
        """Convert storage type integer to XSD type"""
        return cls.STORAGE_TYPE_MAP.get(storage_type, XSD.string)

def clean_identifier(identifier: str) -> str:
    """Clean and validate identifiers"""
    if not identifier:
        return ""
    
    # Remove whitespace and normalize
    cleaned = identifier.strip()
    
    # If it's already a URL, return as is
    if cleaned.startswith(('http://', 'https://')):
        return cleaned
    
    # Otherwise, treat as relative identifier
    return cleaned

def extract_class_name_from_url(url: str) -> str:
    """Extract class name from IBPDI URL"""
    if not url or not url.startswith('https://ibpdi.datacat.org/class/'):
        return ""
    return url.split('/')[-1]

print("✓ Utility functions defined")

class GuidelineParser:
    """Parser for guideline.json files"""
    
    def __init__(self, guideline_path: Path):
        self.guideline_path = guideline_path
        self.data = None
        self.properties = {}
        self.classifications = {}
        
    def load(self):
        """Load and parse the guideline JSON file"""
        print(f"Loading guideline from: {self.guideline_path}")
        
        with open(self.guideline_path, 'r', encoding='utf-8-sig') as f:
            self.data = json.load(f)
        
        # Extract domain information
        domain = self.data.get('Domain', {})
        
        # Parse classifications (classes)
        self._parse_classifications(domain.get('Classifications', {}).get('$values', []))
        
        # Parse properties
        self._parse_properties(domain.get('Properties', {}).get('$values', []))
        
        print(f"✓ Loaded {len(self.classifications)} classifications")
        print(f"✓ Loaded {len(self.properties)} properties")
    
    def _parse_classifications(self, classifications: List[Dict]):
        """Parse classification data"""
        for classification in classifications:
            if not classification.get('ID'):
                continue
                
            class_id = classification['ID']
            class_name = classification.get('Name', '')
            
            # Parse classification properties
            class_properties = []
            classification_props = classification.get('ClassificationProperties', {}).get('$values', [])
            
            for prop in classification_props:
                # Extract property from ClassificationProperty
                property_assignment = prop.get('PropertyAssignment', {})
                property_data = property_assignment.get('Property', {})
                
                if property_data.get('ID'):
                    prop_id = property_data['ID']
                    class_properties.append(prop_id)
                    
                    # Also store the property in our properties dict
                    prop_info = {
                        'name': property_data.get('Name', prop.get('Name', '')),
                        'description': property_data.get('Description', prop.get('Description', '')),
                        'identifier': property_data.get('Identifier', ''),
                        'storage_type': property_data.get('StorageType', 0),
                        'unit_type': property_data.get('UnitType'),
                        'unit_abbreviation': property_data.get('UnitAbbreviation'),
                        'min_value': property_assignment.get('Min'),
                        'max_value': property_assignment.get('Max'),
                        'is_required': prop.get('IsRequired', False),
                        'data': property_data
                    }
                    
                    self.properties[prop_id] = prop_info
            
            self.classifications[class_id] = {
                'name': class_name,
                'properties': class_properties,
                'data': classification
            }
    
    def _parse_properties(self, properties: List[Dict]):
        """Parse property definitions"""
        for prop in properties:
            if not prop.get('ID'):
                continue
                
            prop_id = prop['ID']
            
            # Extract property information
            prop_info = {
                'name': prop.get('Name', ''),
                'description': prop.get('Description', ''),
                'identifier': prop.get('Identifier', ''),
                'storage_type': self._extract_storage_type(prop),
                'unit_type': prop.get('UnitType'),
                'unit_abbreviation': prop.get('UnitAbbreviation'),
                'min_value': prop.get('Min'),
                'max_value': prop.get('Max'),
                'is_required': prop.get('IsRequired', False),
                'data': prop
            }
            
            self.properties[prop_id] = prop_info
    
    def _extract_storage_type(self, prop: Dict) -> int:
        """Extract storage type from property definition"""
        return prop.get('StorageType', 0)  # Default to string

print("✓ GuidelineParser class defined")

class OntologyMerger:
    """Merges ontology.ttl with guideline.json to create enriched ontology"""
    
    def __init__(self, ontology_path: Path, guideline_parser: GuidelineParser):
        self.ontology_path = ontology_path
        self.guideline_parser = guideline_parser
        self.graph = Graph()
        self.existing_classes = set()
        
        # Bind namespaces
        self.graph.bind("ibpdi", IBPDI)
        self.graph.bind("ibpdi-prop", IBPDI_PROP)
        self.graph.bind("owl", OWL)
        self.graph.bind("rdfs", RDFS)
        self.graph.bind("xsd", XSD)
        self.graph.bind("schema", SCHEMA)
        self.graph.bind("dcterms", DCTERMS)
    
    def load_ontology(self):
        """Load the base ontology file"""
        print(f"Loading ontology from: {self.ontology_path}")
        
        try:
            self.graph.parse(str(self.ontology_path), format='turtle')
            print(f"✓ Loaded {len(self.graph)} triples from ontology")
            
            # Extract existing classes
            for subj, pred, obj in self.graph.triples((None, RDF.type, OWL.Class)):
                self.existing_classes.add(str(subj))
                
            print(f"✓ Found {len(self.existing_classes)} existing classes")
            
        except Exception as e:
            print(f"❌ Error loading ontology: {e}")
            raise
    
    def create_data_properties(self):
        """Create OWL data properties from guideline properties"""
        print("Creating data properties from guideline...")
        
        property_count = 0
        
        for prop_id, prop_info in self.guideline_parser.properties.items():
            if not prop_info['identifier']:
                continue
                
            # Create property URI
            prop_uri = URIRef(prop_info['identifier'])
            
            # Define as OWL DataProperty
            self.graph.add((prop_uri, RDF.type, OWL.DatatypeProperty))
            
            # Add basic metadata
            if prop_info['name']:
                self.graph.add((prop_uri, RDFS.label, Literal(prop_info['name'])))
            
            if prop_info['description']:
                self.graph.add((prop_uri, RDFS.comment, Literal(prop_info['description'])))
            
            # Add data type range
            xsd_type = DataTypeMapper.get_xsd_type(prop_info['storage_type'])
            self.graph.add((prop_uri, RDFS.range, xsd_type))
            
            # Add unit information if available
            if prop_info['unit_abbreviation']:
                self.graph.add((prop_uri, SCHEMA.unitCode, Literal(prop_info['unit_abbreviation'])))
            
            if prop_info['unit_type']:
                self.graph.add((prop_uri, SCHEMA.unitText, Literal(prop_info['unit_type'])))
            
            property_count += 1
        
        print(f"✓ Created {property_count} data properties")
    
    def create_property_restrictions(self):
        """Create property restrictions and cardinality constraints"""
        print("Creating property restrictions...")
        
        restriction_count = 0
        
        for prop_id, prop_info in self.guideline_parser.properties.items():
            if not prop_info['identifier']:
                continue
                
            prop_uri = URIRef(prop_info['identifier'])
            
            # Create value restrictions if min/max are specified
            if prop_info['min_value'] is not None or prop_info['max_value'] is not None:
                restriction = BNode()
                self.graph.add((restriction, RDF.type, OWL.Restriction))
                self.graph.add((restriction, OWL.onProperty, prop_uri))
                
                if prop_info['min_value'] is not None:
                    self.graph.add((restriction, OWL.minInclusive, 
                                  Literal(prop_info['min_value'])))
                    
                if prop_info['max_value'] is not None:
                    self.graph.add((restriction, OWL.maxInclusive, 
                                  Literal(prop_info['max_value'])))
                
                restriction_count += 1
        
        print(f"✓ Created {restriction_count} property restrictions")
    
    def link_properties_to_classes(self):
        """Link data properties to appropriate classes based on classification data"""
        print("Linking properties to classes...")
        
        link_count = 0
        
        # Process each classification to find property-class relationships
        for class_id, class_info in self.guideline_parser.classifications.items():
            # Try to find matching ontology class
            class_uri = None
            
            # Look for IBPDI class URL in the classification data
            if class_id.startswith('https://ibpdi.datacat.org/class/'):
                class_uri = URIRef(class_id)
            elif class_info.get('name'):
                # Try to construct URI from name
                class_uri = IBPDI[class_info['name']]
            
            if class_uri and str(class_uri) in self.existing_classes:
                # Link properties to this class
                for prop_id in class_info['properties']:
                    if prop_id in self.guideline_parser.properties:
                        prop_info = self.guideline_parser.properties[prop_id]
                        if prop_info['identifier']:
                            prop_uri = URIRef(prop_info['identifier'])
                            self.graph.add((prop_uri, RDFS.domain, class_uri))
                            link_count += 1
        
        print(f"✓ Created {link_count} property-class links")
    
    def save_merged_ontology(self, output_path: Path):
        """Save the merged ontology to file"""
        print(f"Saving merged ontology to: {output_path}")
        
        try:
            # Serialize as Turtle with nice formatting
            turtle_content = self.graph.serialize(format='turtle')
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(turtle_content)
                
            print(f"✓ Saved merged ontology with {len(self.graph)} triples")
            
        except Exception as e:
            print(f"❌ Error saving ontology: {e}")
            raise

print("✓ OntologyMerger class defined")

def merge_ontology_with_guidelines(ontology_path: Path, guideline_path: Path, 
                                  output_path: Path) -> bool:
    """
    Main function to automatically merge ontology with guidelines
    
    Args:
        ontology_path: Path to the base ontology.ttl file
        guideline_path: Path to the guideline.json file
        output_path: Path for the output enriched ontology file
        
    Returns:
        bool: True if successful, False otherwise
    """
    try:
        print("=" * 60)
        print("ONTOLOGY-GUIDELINE MERGER")
        print("=" * 60)
        
        # Step 1: Parse guideline
        print("\n1. Parsing guideline...")
        guideline_parser = GuidelineParser(guideline_path)
        guideline_parser.load()
        
        # Step 2: Load and merge ontology
        print("\n2. Loading ontology...")
        merger = OntologyMerger(ontology_path, guideline_parser)
        merger.load_ontology()
        
        # Step 3: Create data properties
        print("\n3. Creating data properties...")
        merger.create_data_properties()
        
        # Step 4: Create restrictions
        print("\n4. Creating property restrictions...")
        merger.create_property_restrictions()
        
        # Step 5: Link properties to classes
        print("\n5. Linking properties to classes...")
        merger.link_properties_to_classes()
        
        # Step 6: Save merged ontology
        print("\n6. Saving merged ontology...")
        merger.save_merged_ontology(output_path)
        
        print("\n" + "=" * 60)
        print("✅ MERGE COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ MERGE FAILED: {e}")
        import traceback
        traceback.print_exc()
        return False

# Convenience function for batch processing
def batch_merge_ontologies(input_dir: Path, output_dir: Path) -> Dict[str, bool]:
    """
    Batch process multiple ontology-guideline pairs
    
    Args:
        input_dir: Directory containing ontology.ttl and guideline.json pairs
        output_dir: Directory for output files
        
    Returns:
        Dict mapping file pairs to success status
    """
    results = {}
    
    # Find all ontology files
    ontology_files = list(input_dir.glob("*ontology*.ttl"))
    
    for ont_file in ontology_files:
        # Look for corresponding guideline file
        guideline_candidates = [
            ont_file.parent / ont_file.name.replace("ontology", "guideline").replace(".ttl", ".json"),
            ont_file.parent / "guideline.json",
            ont_file.parent / f"{ont_file.stem}_guideline.json"
        ]
        
        guideline_file = None
        for candidate in guideline_candidates:
            if candidate.exists():
                guideline_file = candidate
                break
        
        if guideline_file:
            output_file = output_dir / f"enriched_{ont_file.name}"
            success = merge_ontology_with_guidelines(ont_file, guideline_file, output_file)
            results[f"{ont_file.name} + {guideline_file.name}"] = success
        else:
            print(f"⚠️  No guideline file found for {ont_file.name}")
            results[ont_file.name] = False
    
    return results

print("✓ Main merger functions defined")

# Execute the merger with current files
print("Testing with current files...")
print(f"Ontology file exists: {ontology_file.exists()}")
print(f"Guideline file exists: {guideline_file.exists()}")

if ontology_file.exists() and guideline_file.exists():
    success = merge_ontology_with_guidelines(
        ontology_path=ontology_file,
        guideline_path=guideline_file, 
        output_path=output_ontology_file
    )
    
    if success:
        print(f"\n📁 Output file created: {output_ontology_file}")
        print(f"📏 File size: {output_ontology_file.stat().st_size:,} bytes")
    else:
        print("\n❌ Merge failed - check error messages above")
else:
    print("\n⚠️  Required input files not found. Please check file paths.")

# Analysis and validation functions
def analyze_merged_ontology(ontology_path: Path):
    """Analyze the structure of the merged ontology"""
    if not ontology_path.exists():
        print("❌ Ontology file not found")
        return
    
    print(f"\n📊 ANALYZING: {ontology_path.name}")
    print("-" * 50)
    
    # Load and analyze
    g = Graph()
    g.parse(str(ontology_path), format='turtle')
    
    # Count different types of entities
    classes = list(g.triples((None, RDF.type, OWL.Class)))
    object_props = list(g.triples((None, RDF.type, OWL.ObjectProperty)))
    data_props = list(g.triples((None, RDF.type, OWL.DatatypeProperty)))
    restrictions = list(g.triples((None, RDF.type, OWL.Restriction)))
    
    print(f"🏛️  Classes: {len(classes)}")
    print(f"🔗 Object Properties: {len(object_props)}")
    print(f"📊 Data Properties: {len(data_props)}")
    print(f"⚖️  Restrictions: {len(restrictions)}")
    print(f"📝 Total Triples: {len(g)}")
    
    # Show sample data properties
    if data_props:
        print(f"\n📋 Sample Data Properties (first 5):")
        for i, (subj, pred, obj) in enumerate(data_props[:5]):
            prop_name = g.value(subj, RDFS.label)
            prop_desc = g.value(subj, RDFS.comment)
            prop_range = g.value(subj, RDFS.range)
            
            print(f"  {i+1}. {subj}")
            if prop_name:
                print(f"     Label: {prop_name}")
            if prop_desc:
                print(f"     Description: {prop_desc}")
            if prop_range:
                print(f"     Range: {prop_range}")
            print()

def compare_ontologies(original_path: Path, enriched_path: Path):
    """Compare original and enriched ontologies"""
    if not original_path.exists() or not enriched_path.exists():
        print("❌ One or both ontology files not found")
        return
    
    print(f"\n🔍 COMPARING ONTOLOGIES")
    print("-" * 50)
    
    # Load both ontologies
    orig_g = Graph()
    orig_g.parse(str(original_path), format='turtle')
    
    enrich_g = Graph()
    enrich_g.parse(str(enriched_path), format='turtle')
    
    print(f"📈 Original triples: {len(orig_g):,}")
    print(f"📈 Enriched triples: {len(enrich_g):,}")
    print(f"➕ Added triples: {len(enrich_g) - len(orig_g):,}")
    
    # Count new data properties
    orig_data_props = set(s for s, p, o in orig_g.triples((None, RDF.type, OWL.DatatypeProperty)))
    enrich_data_props = set(s for s, p, o in enrich_g.triples((None, RDF.type, OWL.DatatypeProperty)))
    new_data_props = enrich_data_props - orig_data_props
    
    print(f"🆕 New data properties: {len(new_data_props)}")

# Run analysis if output exists
if output_ontology_file.exists():
    analyze_merged_ontology(output_ontology_file)
    compare_ontologies(ontology_file, output_ontology_file)
else:
    print("⏳ Run the merger first to generate the enriched ontology")