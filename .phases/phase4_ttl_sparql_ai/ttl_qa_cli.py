#!/usr/bin/env python3
"""
Interactive TTL Question-Answering CLI Tool
A beautiful, colorful CLI for querying TTL data with natural language
"""

import os
import sys
import asyncio
import time
import json
from pathlib import Path
from typing import Dict, Any, Optional

# Rich imports for beautiful CLI
from rich.console import Console
from rich.panel import Panel
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TaskProgressColumn
from rich.prompt import Prompt, Confirm
from rich.text import Text
from rich.table import Table
from rich.markdown import Markdown
from rich.live import Live
from rich.layout import Layout
from rich.syntax import Syntax
from rich import box
from rich.align import Align

# Prompt toolkit for better input handling
from prompt_toolkit import prompt
from prompt_toolkit.completion import WordCompleter
from prompt_toolkit.history import InMemoryHistory
from prompt_toolkit.auto_suggest import AutoSuggestFromHistory
from prompt_toolkit.formatted_text import HTML
from prompt_toolkit.styles import Style

# Load environment
import dotenv
dotenv.load_dotenv()

# Pydantic AI imports
from pydantic_ai import Agent
from pydantic_ai.models.openai import OpenAIModel

# Import our TTL QA system components
from ttl_qa_system_components import TTLQuestionAnsweringSystem, analyze_ttl_file

class TTLQACLIApp:
    """Beautiful interactive CLI for TTL Question-Answering"""
    
    def __init__(self):
        self.console = Console()
        self.qa_system = TTLQuestionAnsweringSystem()
        self.current_ttl_file = None
        self.question_history = InMemoryHistory()
        
        # CLI styling
        self.style = Style.from_dict({
            'question': '#00ff88 bold',
            'answer': '#88c0d0',
            'insight': '#d08770',
            'error': '#bf616a bold',
            'success': '#a3be8c bold',
            'info': '#5e81ac',
            'prompt': '#b48ead bold'
        })
        
        # Common question suggestions
        self.question_suggestions = [
            "How many addresses are there in total?",
            "What cities are represented in the data?",
            "What types of buildings are there?",
            "Show me buildings with parking spaces",
            "What countries are in the data?",
            "Show me office buildings",
            "What's the distribution by construction year?",
            "Show me residential buildings",
            "How many buildings have energy efficiency ratings?",
            "What are the different address types?"
        ]
        
        self.question_completer = WordCompleter(
            self.question_suggestions + 
            ['help', 'quit', 'exit', 'clear', 'load', 'stats', 'cache']
        )
    
    def print_header(self):
        """Print beautiful app header"""
        header_text = """
🏗️ TTL Question-Answering System
Natural Language Interface for Building Data
        """
        
        header_panel = Panel(
            Align.center(header_text),
            border_style="bright_blue",
            box=box.DOUBLE_EDGE,
            padding=(1, 2)
        )
        
        self.console.print(header_panel)
        self.console.print()
    
    def print_status(self, ttl_file: Optional[str] = None):
        """Print current system status"""
        if ttl_file:
            cache_stats = self.qa_system.get_cache_stats()
            
            status_table = Table(show_header=False, box=box.MINIMAL)
            status_table.add_column("Item", style="cyan")
            status_table.add_column("Value", style="green")
            
            status_table.add_row("📁 Current File", Path(ttl_file).name if ttl_file else "None")
            status_table.add_row("💾 Query Cache", f"{cache_stats['query_cache_size']} entries")
            status_table.add_row("🗃️ Graph Cache", f"{cache_stats['graph_cache_size']} files")
            
            status_panel = Panel(
                status_table,
                title="📊 System Status",
                border_style="green",
                padding=(0, 1)
            )
            
            self.console.print(status_panel)
        else:
            self.console.print(Panel(
                "[yellow]No TTL file loaded. Use 'load' command to load a file.[/yellow]",
                title="⚠️ Status",
                border_style="yellow"
            ))
    
    def load_ttl_file(self) -> Optional[str]:
        """Interactive TTL file loading"""
        self.console.print("\n[bold cyan]Loading TTL File[/bold cyan]")
        
        # Default file path
        default_path = r"C:\Users\<USER>\Documents\BA\.phases\phase4_ttl_sparql_ai\assets\example.ttl"
        
        file_path = Prompt.ask(
            "[cyan]Enter TTL file path[/cyan]",
            default=default_path,
            console=self.console
        )
        
        if not Path(file_path).exists():
            self.console.print(f"[red]❌ File not found: {file_path}[/red]")
            return None
        
        try:
            # Show loading progress
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=self.console
            ) as progress:
                task = progress.add_task("Loading TTL file...", total=None)
                
                # Analyze the file
                analysis = analyze_ttl_file(file_path)
                
                progress.update(task, description="Analyzing structure...")
                time.sleep(0.5)  # Visual feedback
            
            self.console.print(f"[green]✅ Successfully loaded: {Path(file_path).name}[/green]")
            
            # Show quick analysis
            analysis_panel = Panel(
                analysis[:500] + "..." if len(analysis) > 500 else analysis,
                title="📊 TTL File Analysis",
                border_style="blue",
                expand=False
            )
            self.console.print(analysis_panel)
            
            return file_path
            
        except Exception as e:
            self.console.print(f"[red]❌ Error loading file: {str(e)}[/red]")
            return None
    
    async def process_question(self, question: str, ttl_file: str) -> Dict[str, Any]:
        """Process a question with beautiful progress display"""
        
        # Create layout for live updates
        layout = Layout()
        layout.split_column(
            Layout(name="header", size=3),
            Layout(name="progress", size=10),
            Layout(name="status", size=5)
        )
        
        # Header
        layout["header"].update(Panel(
            f"[bold cyan]Processing:[/bold cyan] {question}",
            box=box.MINIMAL
        ))
        
        # Progress tracking
        progress = Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TaskProgressColumn(),
            console=self.console
        )
        
        # Status text
        status_text = Text("Initializing...", style="cyan")
        
        with Live(layout, console=self.console, refresh_per_second=10) as live:
            layout["progress"].update(progress)
            layout["status"].update(Panel(status_text, box=box.MINIMAL))
            
            # Step 1: TTL Analysis
            task1 = progress.add_task("📊 Analyzing TTL structure...", total=100)
            status_text.plain = "Reading and parsing TTL file structure..."
            live.update(layout)
            
            for i in range(0, 101, 10):
                progress.update(task1, completed=i)
                await asyncio.sleep(0.1)
            
            # Step 2: SPARQL Generation
            task2 = progress.add_task("🔍 Generating SPARQL query...", total=100)
            status_text.plain = "Using AI to generate optimized SPARQL query..."
            live.update(layout)
            
            for i in range(0, 101, 15):
                progress.update(task2, completed=i)
                await asyncio.sleep(0.1)
            
            # Step 3: Query Execution
            task3 = progress.add_task("⚡ Executing query...", total=100)
            status_text.plain = "Running SPARQL query against TTL data..."
            live.update(layout)
            
            # Actually process the question
            result = await self.qa_system.answer_question(ttl_file, question)
            
            for i in range(0, 101, 20):
                progress.update(task3, completed=i)
                await asyncio.sleep(0.05)
            
            # Step 4: Response Generation
            task4 = progress.add_task("💬 Generating response...", total=100)
            status_text.plain = "Creating natural language response..."
            live.update(layout)
            
            for i in range(0, 101, 25):
                progress.update(task4, completed=i)
                await asyncio.sleep(0.03)
            
            status_text.plain = "Complete! 🎉"
            live.update(layout)
            await asyncio.sleep(0.5)
        
        return result
    
    def display_result(self, result: Dict[str, Any], question: str):
        """Display the result in a beautiful format"""
        
        if result["success"]:
            # Success result
            answer_panel = Panel(
                result["answer"],
                title="💬 Answer",
                border_style="green",
                padding=(1, 2)
            )
            self.console.print(answer_panel)
            
            # Data summary
            if result.get("data_summary"):
                summary_panel = Panel(
                    result["data_summary"],
                    title="📊 Data Summary",
                    border_style="blue",
                    padding=(1, 2)
                )
                self.console.print(summary_panel)
            
            # Additional insights
            if result.get("additional_insights"):
                insights_panel = Panel(
                    result["additional_insights"],
                    title="💡 Additional Insights",
                    border_style="yellow",
                    padding=(1, 2)
                )
                self.console.print(insights_panel)
            
            # Performance metrics
            perf_table = Table(show_header=False, box=box.MINIMAL)
            perf_table.add_column("Metric", style="cyan")
            perf_table.add_column("Value", style="green")
            
            perf_table.add_row("⚡ Query Time", f"{result['execution_time']:.3f}s")
            perf_table.add_row("🚀 Total Time", f"{result['total_pipeline_time']:.3f}s")
            perf_table.add_row("📊 Data Rows", str(result['data_rows']))
            perf_table.add_row("🎯 Confidence", f"{result['confidence']:.1%}")
            
            perf_panel = Panel(
                perf_table,
                title="📈 Performance",
                border_style="magenta",
                padding=(0, 1)
            )
            self.console.print(perf_panel)
            
            # Show SPARQL query if requested
            if Confirm.ask("\n[dim]Would you like to see the generated SPARQL query?[/dim]", 
                         default=False, console=self.console):
                sparql_syntax = Syntax(
                    result["sparql_query"], 
                    "sparql", 
                    theme="monokai", 
                    line_numbers=True,
                    word_wrap=True
                )
                
                sparql_panel = Panel(
                    sparql_syntax,
                    title=f"🔍 Generated {result['query_type']} Query",
                    border_style="cyan",
                    padding=(1, 1)
                )
                self.console.print(sparql_panel)
        
        else:
            # Error result
            error_content = f"[red]{result['error']}[/red]\n\n"
            error_content += f"[yellow]💡 Suggestions:[/yellow]\n{result['suggestions']}"
            
            if result.get('sparql_query'):
                error_content += f"\n\n[dim]Generated query:[/dim]\n{result['sparql_query'][:200]}..."
            
            error_panel = Panel(
                error_content,
                title=f"❌ Error in {result.get('stage', 'unknown stage')}",
                border_style="red",
                padding=(1, 2)
            )
            self.console.print(error_panel)
    
    def show_help(self):
        """Show help information"""
        help_content = """
[bold cyan]Available Commands:[/bold cyan]

[green]load[/green]     - Load a TTL file
[green]stats[/green]    - Show system statistics  
[green]clear[/green]    - Clear the screen
[green]cache[/green]    - Clear all caches
[green]help[/green]     - Show this help
[green]quit/exit[/green] - Exit the application

[bold cyan]Question Examples:[/bold cyan]

• How many addresses are there in total?
• What cities are represented in the data?
• What types of buildings are there?
• Show me buildings with parking spaces
• What countries are in the data?
• Show me office buildings
• What's the distribution by construction year?

[bold yellow]Tips:[/bold yellow]
• Use Tab for auto-completion
• Use arrow keys for command history
• Questions support natural language
• Be specific for better results
        """
        
        help_panel = Panel(
            help_content,
            title="🆘 Help",
            border_style="blue",
            padding=(1, 2)
        )
        self.console.print(help_panel)
    
    def main_loop(self):
        """Main interactive loop"""
        self.print_header()
        
        # Load TTL file at startup
        self.console.print("[bold]Welcome! Let's start by loading a TTL file.[/bold]\n")
        self.current_ttl_file = self.load_ttl_file()
        
        if not self.current_ttl_file:
            self.console.print("[red]❌ Cannot start without a TTL file. Exiting.[/red]")
            return
        
        self.console.print("\n" + "="*80)
        self.print_status(self.current_ttl_file)
        self.console.print("\n[dim]Type 'help' for commands, or just ask a question![/dim]\n")
        
        while True:
            try:
                # Get user input with nice prompt
                question = prompt(
                    HTML('<style fg="#b48ead"><b>❓ Ask a question:</b></style> '),
                    history=self.question_history,
                    auto_suggest=AutoSuggestFromHistory(),
                    completer=self.question_completer,
                    style=self.style
                ).strip()
                
                if not question:
                    continue
                
                # Handle commands
                if question.lower() in ['quit', 'exit', 'q']:
                    self.console.print("\n[cyan]👋 Thank you for using TTL QA System![/cyan]")
                    break
                
                elif question.lower() == 'help':
                    self.show_help()
                    continue
                
                elif question.lower() == 'load':
                    new_file = self.load_ttl_file()
                    if new_file:
                        self.current_ttl_file = new_file
                    continue
                
                elif question.lower() == 'stats':
                    self.print_status(self.current_ttl_file)
                    continue
                
                elif question.lower() == 'clear':
                    os.system('cls' if os.name == 'nt' else 'clear')
                    self.print_header()
                    continue
                
                elif question.lower() == 'cache':
                    self.qa_system.clear_caches()
                    self.console.print("[green]🧹 All caches cleared![/green]")
                    continue
                
                # Process the question
                if not self.current_ttl_file:
                    self.console.print("[red]❌ No TTL file loaded. Use 'load' command first.[/red]")
                    continue
                
                self.console.print()  # Add spacing
                # Process the question
                result = asyncio.run(self.process_question(question, self.current_ttl_file))
                self.console.print()  # Add spacing
                
                self.display_result(result, question)
                self.console.print("\n" + "-"*80 + "\n")
                
            except KeyboardInterrupt:
                self.console.print("\n[yellow]Use 'quit' or 'exit' to leave.[/yellow]")
                continue
            except Exception as e:
                self.console.print(f"\n[red]❌ Unexpected error: {str(e)}[/red]")
                continue

def main():
    """Main entry point"""
    # Ensure we have the required environment
    if not os.getenv("OR_API_KEY"):
        console = Console()
        console.print("[red]❌ OR_API_KEY environment variable is required![/red]")
        console.print("[yellow]Please set your OpenRouter API key in the .env file.[/yellow]")
        return
    
    app = TTLQACLIApp()
    app.main_loop()

if __name__ == "__main__":
    main()
