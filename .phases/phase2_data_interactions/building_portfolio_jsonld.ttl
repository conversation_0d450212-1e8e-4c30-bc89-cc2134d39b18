@prefix ns1: <https://ibpdi.datacat.org/property/> .
@prefix ns2: <https://ibpdi.datacat.org/class/> .

<https://example.com/009cb9c7-07d7-48fd-a539-672fbe6f2652> a ns2:Address ;
    ns2:hasBuilding <https://example.com/e64f2098-e585-4b4d-a69f-383d7eb6d1cd> ;
    ns1:city "HS Amsterdam" ;
    ns1:country "Netherlands" ;
    ns1:house-number "192" ;
    ns1:postal-code "1079" ;
    ns1:street-name "Aijstraat" .

<https://example.com/018ac746-2503-4f2e-b681-8cb37d2f0e29> a ns2:Address ;
    ns2:hasBuilding <https://example.com/1892ebb9-9532-4dee-bddc-a7044da700a2> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "172-190" ;
    ns1:postal-code "60327" ;
    ns1:street-name "Mainzer Landstraße" .

<https://example.com/01bea4e6-fee0-40dc-a829-902d901d0a67> a ns2:Address ;
    ns2:hasBuilding <https://example.com/8995106d-e483-4266-a3c7-65120a6f3bbe> ;
    ns1:city "Hamburg" ;
    ns1:country "Germany" ;
    ns1:house-number "1-3" ;
    ns1:postal-code "20355" ;
    ns1:street-name "Stadthausbricke" .

<https://example.com/02a175f3-9042-44f7-bb2f-a3b9c5997bb8> a ns2:Address ;
    ns2:hasBuilding <https://example.com/d54154fb-bd6f-4e1a-b2ef-c314e8ec6d4c> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "141)" ;
    ns1:postal-code "81671" ;
    ns1:street-name "Rosenheimer Straßeaße" .

<https://example.com/04baa1d3-3007-41e6-ba27-1b124e96706c> a ns2:Address ;
    ns2:hasBuilding <https://example.com/986ee284-f90a-4d68-a0a1-7294fe92de88> ;
    ns1:city "Neuss" ;
    ns1:country "Germany" ;
    ns1:house-number "22" ;
    ns1:postal-code "41468" ;
    ns1:street-name "Am Blankenwasser" .

<https://example.com/06c10c44-8944-4842-8538-20d5aba3f7bb> a ns2:Address ;
    ns2:hasBuilding <https://example.com/a2e0b5f8-b290-440f-bdfb-4861730dc59a> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "30" ;
    ns1:postal-code "60325" ;
    ns1:street-name "Ulmenstraße" .

<https://example.com/08ab822a-72e3-4967-8eb2-acffafe469e9> a ns2:Address ;
    ns2:hasBuilding <https://example.com/832cb406-5337-4132-81b6-74298751d028> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "4" ;
    ns1:postal-code "75002" ;
    ns1:street-name "Rue du septembre" .

<https://example.com/0b923e2c-257c-4ddd-9f96-2fea85b3ef65> a ns2:Address ;
    ns2:hasBuilding <https://example.com/1ba5c96e-d666-4981-8962-b62d195f4eca> ;
    ns1:city "London " ;
    ns1:country "UK" ;
    ns1:house-number "1" ;
    ns1:postal-code "WC1B 5HA" ;
    ns1:street-name "Southampton Row" .

<https://example.com/0d2d08d8-62da-415d-83f3-d6796810c147> a ns2:Address ;
    ns2:hasBuilding <https://example.com/42ee10cc-3dfa-496b-b165-8deeb57f63ea> ;
    ns1:city "Hamburg" ;
    ns1:country "Germany" ;
    ns1:house-number "11" ;
    ns1:postal-code "20354" ;
    ns1:street-name "Hobe Bleichen" .

<https://example.com/0d35fb07-63a9-4280-a4b2-cb808f0211e6> a ns2:Address ;
    ns2:hasBuilding <https://example.com/d087f4c1-344d-401b-aff2-62dd21d74c68> ;
    ns1:city "Berlin" ;
    ns1:country "Germany" ;
    ns1:house-number "54" ;
    ns1:postal-code "10117" ;
    ns1:street-name "Dorotheenstraße" .

<https://example.com/1106fa42-028e-4f90-b4b9-4490da000d35> a ns2:Address ;
    ns2:hasBuilding <https://example.com/21c44521-ea5e-4e8e-97ce-d67fd02eeac4> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "22" ;
    ns1:postal-code "75001" ;
    ns1:street-name "Rue des Pyramides" .

<https://example.com/128ffd41-54ff-4045-b654-54ed4c12b7de> a ns2:Address ;
    ns2:hasBuilding <https://example.com/c2cb5e24-bff4-4bc9-aaba-4d6de75dce3a> ;
    ns1:country "Poland" ;
    ns1:house-number "11" ;
    ns1:postal-code "96-500" ;
    ns1:street-name "Gaj" .

<https://example.com/12e4be74-2fbb-4b4a-b5b7-a9bc6c8e8a1a> a ns2:Address ;
    ns2:hasBuilding <https://example.com/a5271b50-54b9-4517-af00-c7c2d163a1f4> ;
    ns1:city "Dublin" ;
    ns1:country "Ireland" ;
    ns1:house-number "11" ;
    ns1:postal-code "North" ;
    ns1:street-name "Henry St" .

<https://example.com/16521686-eb85-4515-86f4-a6bac3c82392> a ns2:Address ;
    ns2:hasBuilding <https://example.com/0fb819dd-8215-4224-be62-f8bc93aab237> ;
    ns1:city "Lyon" ;
    ns1:country "France" ;
    ns1:house-number "35" ;
    ns1:postal-code "69007" ;
    ns1:street-name "Now Coworking Rue de Marseille" .

<https://example.com/1761d76e-4804-4a9d-adaa-669822e424d0> a ns2:Address ;
    ns2:hasBuilding <https://example.com/319ebfe7-3d4a-4b71-9216-e65dfb668f81> ;
    ns1:city " Madrid" ;
    ns1:country "Spain" ;
    ns1:postal-code "28012" ;
    ns1:street-name "C.de Carretas" .

<https://example.com/1830a091-367f-434a-b2eb-4e495c16d3ff> a ns2:Address ;
    ns2:hasBuilding <https://example.com/57cd12c2-f87c-48cf-be65-66574c335774> ;
    ns1:city "Stuttgart" ;
    ns1:country "Germany" ;
    ns1:house-number "2" ;
    ns1:postal-code "70173" ;
    ns1:street-name "Thouretstrae" .

<https://example.com/1b0cf251-2bb1-4a54-a669-3dd89d3d1412> a ns2:Address ;
    ns2:hasBuilding <https://example.com/10d7c8c3-bc2f-4166-97d7-d11f04a39d82> ;
    ns1:city "Berlin" ;
    ns1:country "Germany" ;
    ns1:house-number "50-55" ;
    ns1:postal-code "10117" ;
    ns1:street-name "Friedrichstraße" .

<https://example.com/1b9df382-bb91-4587-922b-0db7d83cf54d> a ns2:Address ;
    ns2:hasBuilding <https://example.com/fdb38a15-d380-49ae-8639-cc5861edc1a3> ;
    ns1:city "Milano MI" ;
    ns1:country "Italy" ;
    ns1:house-number "8" ;
    ns1:postal-code "20124" ;
    ns1:street-name "Via Ferrante Aport" .

<https://example.com/1cbd2116-7b88-4f4b-9c35-c6e947928c70> a ns2:Address ;
    ns2:hasBuilding <https://example.com/eed9967e-f5b7-4bca-a464-a0638dc60f09> ;
    ns1:city "NW Venlo" ;
    ns1:country "Netherlands" ;
    ns1:house-number "8" ;
    ns1:postal-code "5928" ;
    ns1:street-name "Doctor Lelyweg" .

<https://example.com/1dfae7e5-51a5-4b3d-98dc-1a12529d85ea> a ns2:Address ;
    ns2:hasBuilding <https://example.com/66c560f4-2f87-40af-9e3c-992d886a1d60> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "39-41" ;
    ns1:postal-code "75017" ;
    ns1:street-name "Av. de Wagram" .

<https://example.com/24a9dee6-6646-460d-a75b-a05d2586ae47> a ns2:Address ;
    ns2:hasBuilding <https://example.com/0058c837-25b9-4345-9cc1-ec056f456052> ;
    ns1:city "Wien" ;
    ns1:country "Austria" ;
    ns1:postal-code "1020" ;
    ns1:street-name "Pius Zwei" .

<https://example.com/284976c1-a297-4cee-9ef2-5d28e47f54a8> a ns2:Address ;
    ns2:hasBuilding <https://example.com/90e3d68a-313d-401d-82cd-f03587ddda23> ;
    ns1:city "Milano MI" ;
    ns1:country "Italy" ;
    ns1:house-number "13" ;
    ns1:postal-code "20123" ;
    ns1:street-name "Via Orefici" .

<https://example.com/2ac8ad0b-5f80-4d95-bedc-199479bb3931> a ns2:Address ;
    ns2:hasBuilding <https://example.com/06ccb9e5-e34b-4637-aaa4-d5e345cc7d22> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "2" ;
    ns1:postal-code "60486" ;
    ns1:street-name "Theodor-Heuss-Allee" .

<https://example.com/2cb71bb8-de4e-4479-8db3-7c9b78801f2c> a ns2:Address ;
    ns2:hasBuilding <https://example.com/76eb609f-9a49-4441-9258-a4cb9d63dcd1> ;
    ns1:city "Córdoba" ;
    ns1:country "Spain" ;
    ns1:postal-code "14003" ;
    ns1:street-name "Calle Conde de Gondomar" .

<https://example.com/2ce12f63-fcc1-4ef9-bd96-01fe849c6896> a ns2:Address ;
    ns2:hasBuilding <https://example.com/4620ead9-e050-4d4c-9b7b-d8bff52936b8> ;
    ns1:city "Berlin" ;
    ns1:country "Germany" ;
    ns1:house-number "34" ;
    ns1:postal-code "12163" ;
    ns1:street-name "Einkaufszentrum Das Schloss Schlostrae" .

<https://example.com/2d38d186-ea79-45de-bd78-6bd20df18893> a ns2:Address ;
    ns2:hasBuilding <https://example.com/0b3dfe7c-08fd-4139-8fc4-f99d963cd23c> ;
    ns1:city "Budapest" ;
    ns1:country "Hungary" ;
    ns1:house-number "7" ;
    ns1:postal-code "1054" ;
    ns1:street-name "Polna" .

<https://example.com/2dfe9323-7ae5-47ee-bc08-413519ce45b6> a ns2:Address ;
    ns2:hasBuilding <https://example.com/adbd1c9e-49b4-4f4d-8155-6329f72b6b6d> ;
    ns1:city "Milano MI" ;
    ns1:country "Italy" ;
    ns1:house-number "18," ;
    ns1:postal-code "20159" ;
    ns1:street-name "Via Carlo Imbonati" .

<https://example.com/2e1072c6-e9fe-4f24-9536-248872883738> a ns2:Address ;
    ns2:hasBuilding <https://example.com/c592f971-2fda-4169-8e89-c7f296a3300f> ;
    ns1:city "Raunheim" ;
    ns1:country "Germany" ;
    ns1:house-number "2" ;
    ns1:postal-code "65479" ;
    ns1:street-name "Kelsterbacher Str." .

<https://example.com/2f643fd3-9d5b-46f6-81bc-2abd60df713a> a ns2:Address ;
    ns2:hasBuilding <https://example.com/ea27f06d-b7a6-43e5-aee0-8ef57cb35279> ;
    ns1:city "Bertrange" ;
    ns1:country "Luxembourg" ;
    ns1:house-number "23" ;
    ns1:postal-code "8070" ;
    ns1:street-name "Rue du pults Romain" .

<https://example.com/2fd1e79c-ba78-4324-90dd-0f19b9fa2a5f> a ns2:Address ;
    ns2:hasBuilding <https://example.com/9003555b-57c5-426a-b8e5-31fcdc035e5b> ;
    ns1:city "London" ;
    ns1:country "UK" ;
    ns1:house-number "101d" ;
    ns1:postal-code "EC2R 8DN" ;
    ns1:street-name "Jewry" .

<https://example.com/30d380a3-1509-4e99-af47-01846458182b> a ns2:Address ;
    ns2:hasBuilding <https://example.com/6ac5af7c-329c-47b1-82c0-e21c10454b38> ;
    ns1:city "Raunheim" ;
    ns1:country "Germany" ;
    ns1:house-number "1" ;
    ns1:postal-code "65479" ;
    ns1:street-name "Stockstraße" .

<https://example.com/3111e590-210c-4593-926e-6700aa0f4514> a ns2:Address ;
    ns2:hasBuilding <https://example.com/0a2add59-d7d2-4802-ac6d-2bbe4f1a4575> ;
    ns1:city "Gliwice" ;
    ns1:country "Poland" ;
    ns1:postal-code "44-102" .

<https://example.com/31d6ca19-8a41-4167-8cc0-932923518399> a ns2:Address ;
    ns2:hasBuilding <https://example.com/8e69a400-3087-4b3d-93a4-fcf0ba1dafd3> ;
    ns1:city "AB Rotterdam" ;
    ns1:country "Netherlands" ;
    ns1:house-number "63" ;
    ns1:postal-code "3012" ;
    ns1:street-name "Coolsingel" .

<https://example.com/333666e7-9f66-4408-b40d-f16af038ef88> a ns2:Address ;
    ns2:hasBuilding <https://example.com/de3e19d1-687f-4ffe-83f5-96363f23275d> ;
    ns1:city "Warszawa" ;
    ns1:country "Poland" ;
    ns1:postal-code "00-132" .

<https://example.com/34fb1c83-74f2-4bc6-a77a-fabee29812f9> a ns2:Address ;
    ns2:hasBuilding <https://example.com/4f72a0ac-ef2f-4e33-aa1b-c34572988b6a> ;
    ns1:city "Neu-Isenburg" ;
    ns1:country "Germany" ;
    ns1:house-number "168-176" ;
    ns1:postal-code "63263" ;
    ns1:street-name "Frankfurter Straßeaße" .

<https://example.com/38c81806-5b93-4038-933b-7c6987835b98> a ns2:Address ;
    ns2:hasBuilding <https://example.com/f77edc3d-5b2c-42bb-b7e9-8efb5c218cb2> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "35" ;
    ns1:postal-code "75019" ;
    ns1:street-name "Rue dela Gare" .

<https://example.com/3ac9d6af-380d-4db7-a3be-5b24400c1edb> a ns2:Address ;
    ns2:hasBuilding <https://example.com/99120dc1-8ee9-46c3-b465-c7e890895a76> ;
    ns1:city "Berlin" ;
    ns1:country "Germany" ;
    ns1:house-number "24" ;
    ns1:postal-code "10117" ;
    ns1:street-name "GeorgenstraBe" .

<https://example.com/3d6a0fbc-c197-4bd0-9c26-c7d1c5767f5b> a ns2:Address ;
    ns2:hasBuilding <https://example.com/6edcfd7b-47ed-46f1-9a91-c3e99a082b92> ;
    ns1:city "Saint-Josse-ten-Noode" ;
    ns1:country "Belgium" ;
    ns1:house-number "55" ;
    ns1:postal-code "1210" ;
    ns1:street-name "Rue du Progrés" .

<https://example.com/42212432-19cd-4864-9de2-0d3f461f7e07> a ns2:Address ;
    ns2:hasBuilding <https://example.com/7310956d-6d8f-4bd7-af7b-4943aae4c2a4> ;
    ns1:city " Praha 4-Nusle" ;
    ns1:country "Czechia" ;
    ns1:house-number "8" ;
    ns1:postal-code "140 00" ;
    ns1:street-name "St James's Square St. James's" .

<https://example.com/47a26dba-3230-4850-a9a0-f6c0cbc90f1e> a ns2:Address ;
    ns2:hasBuilding <https://example.com/7b46d5e9-cb1b-4897-bb20-14c4110629ab> ;
    ns1:city "Newbridge" ;
    ns1:country "Ireland" ;
    ns1:house-number "2" ;
    ns1:postal-code "Moorfield" ;
    ns1:street-name "unit chaman house" .

<https://example.com/4c76dfb7-9af2-4a0b-9a1a-5c4f93c48231> a ns2:Address ;
    ns2:hasBuilding <https://example.com/f52ac78d-3bb0-4c2a-9777-39f65ccf1716> ;
    ns1:city "Zürich" ;
    ns1:country "Switzerland" ;
    ns1:house-number "29" ;
    ns1:postal-code "8008" ;
    ns1:street-name "Utoqual" .

<https://example.com/4ee0b1b7-86d5-4e77-a9a9-93259cfef09c> a ns2:Address ;
    ns2:hasBuilding <https://example.com/1ccb4dde-ccc2-4267-aa90-d682090fe9ab> ;
    ns1:city "Wien" ;
    ns1:country "Austria" ;
    ns1:house-number "42/48" ;
    ns1:postal-code "1070" ;
    ns1:street-name "Gemngross (Modepalast Concept Store Mariahilfer Straßeaße" .

<https://example.com/52c8308e-0275-4c03-ac5f-32fd2820bd92> a ns2:Address ;
    ns2:hasBuilding <https://example.com/cc4e3285-f6a0-4e9b-a204-4a70b555ab25> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "4" ;
    ns1:postal-code "80331" ;
    ns1:street-name "Rosental" .

<https://example.com/534b5c05-f162-4d18-bc0e-a68ae9ac21e2> a ns2:Address ;
    ns2:hasBuilding <https://example.com/899d7524-f048-4006-aeb8-7fc0410c05b4> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "3" ;
    ns1:postal-code "60528" ;
    ns1:street-name "Herriotstrafe" .

<https://example.com/565f7e3e-50b6-4fcc-b865-d7d53cf1515f> a ns2:Address ;
    ns2:hasBuilding <https://example.com/3c04623f-e668-40e8-81a8-1945d6e577d8> ;
    ns1:city "Bremen" ;
    ns1:country "Germany" ;
    ns1:house-number "31" ;
    ns1:postal-code "28197" ;
    ns1:street-name "Ludwig- Erhard Straßeate" .

<https://example.com/58148b74-42e6-4f2e-a8b9-5c82e4bce54e> a ns2:Address ;
    ns2:hasBuilding <https://example.com/be3982d9-1086-4de2-a5be-614a95e13330> ;
    ns1:city "Milton Keynes" ;
    ns1:country "UK" ;
    ns1:postal-code "MK17 8EW" ;
    ns1:street-name "Fen treet" .

<https://example.com/5baac072-c349-4548-befb-978d63ebc8e8> a ns2:Address ;
    ns2:hasBuilding <https://example.com/8fa01b37-3d32-4a26-9aea-0852d3d4c191> ;
    ns1:city "Carouge" ;
    ns1:country "Switzerland" ;
    ns1:house-number "60" ;
    ns1:postal-code "1227" ;
    ns1:street-name "Rte des Acacias" .

<https://example.com/5d003703-9249-494a-9d9e-51c1519178dc> a ns2:Address ;
    ns2:hasBuilding <https://example.com/cd9ae8c1-0380-4ecc-98a3-5510442e3b2c> ;
    ns1:city "Oslo" ;
    ns1:country "Norway" ;
    ns1:house-number "6" ;
    ns1:postal-code "0161" ;
    ns1:street-name "Stortingsgata " .

<https://example.com/5ecf3188-9d58-4652-9d19-1d3794f95420> a ns2:Address ;
    ns2:hasBuilding <https://example.com/0a8cbe14-3d18-4c6c-a778-abade5b4b8b1> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "10/12" ;
    ns1:postal-code "75008" ;
    ns1:street-name "Rue du Général Foy" .

<https://example.com/60f54207-6c9f-4e75-bf57-6fc7a0cc9f91> a ns2:Address ;
    ns2:hasBuilding <https://example.com/5045a210-29bb-4b4e-b073-3026d291047d> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "293" ;
    ns1:postal-code "60326" ;
    ns1:street-name "Mainzer Landstratle" .

<https://example.com/635d067f-ebd5-4383-98fc-ea28ba0f2f14> a ns2:Address ;
    ns2:hasBuilding <https://example.com/8e8d2928-9d12-4b2a-8e7e-5136c6dd736a> ;
    ns1:city "139 00 Praha 4-Nusle" ;
    ns1:country "Czechia" ;
    ns1:house-number "1" ;
    ns1:postal-code "140 00" ;
    ns1:street-name "Rathbone Sq" .

<https://example.com/638051f6-99d2-4784-8907-c9488441ab6a> a ns2:Address ;
    ns2:hasBuilding <https://example.com/d3954c67-c035-4b3e-96bc-29515c31b7f5> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "58-68" ;
    ns1:postal-code "81541" ;
    ns1:street-name "St.Martin Straßeaße" .

<https://example.com/649fc234-610d-414b-ba34-70f124825a1f> a ns2:Address ;
    ns2:hasBuilding <https://example.com/d577dc63-6bf1-4c89-8d9b-f4ee302444d0> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "53" ;
    ns1:postal-code "75009" ;
    ns1:street-name "Rue de la Victoire" .

<https://example.com/64eeb72f-abac-48a6-bf44-1e9e403bb579> a ns2:Address ;
    ns2:hasBuilding <https://example.com/c159db6c-d0a5-4298-baff-f65a2d735bcc> ;
    ns1:city "Warszawa" ;
    ns1:country "Poland" ;
    ns1:house-number "28" ;
    ns1:postal-code "00-847" ;
    ns1:street-name "Towarowa" .

<https://example.com/65263dfa-4542-49bd-893d-e3df9c253592> a ns2:Address ;
    ns2:hasBuilding <https://example.com/fb1f443f-4478-4044-a8e7-d551d75802d1> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "33" ;
    ns1:postal-code "75009" ;
    ns1:street-name "Rue La Fayette" .

<https://example.com/65e65ff0-7dcd-4fdb-b4be-b411917bf644> a ns2:Address ;
    ns2:hasBuilding <https://example.com/52ca00a9-2cbe-4f54-8ea8-7b9f62c29f5d> ;
    ns1:city "GA Oirlo" ;
    ns1:country "Netherlands" ;
    ns1:house-number "10" ;
    ns1:postal-code "5807" ;
    ns1:street-name "Nobelstraat" .

<https://example.com/68765d34-9b52-4e8f-ade6-f34a9d7ae414> a ns2:Address ;
    ns2:hasBuilding <https://example.com/6f8a060f-0954-411a-aba3-0425fc60288c> ;
    ns1:city "Cork" ;
    ns1:country "Ireland" ;
    ns1:postal-code "Mahon" ;
    ns1:street-name "as" .

<https://example.com/6ae41207-b687-4503-8ee6-5e6c14283f21> a ns2:Address ;
    ns2:hasBuilding <https://example.com/4e5596f9-f928-47a8-907f-2538d291e0be> ;
    ns1:city "London " ;
    ns1:country "UK" ;
    ns1:house-number "120" ;
    ns1:postal-code "EC2Y 5ET" ;
    ns1:street-name "Moor House London Wall" .

<https://example.com/726681f4-2e71-4cd5-9498-97bc5627ca66> a ns2:Address ;
    ns2:hasBuilding <https://example.com/a8fa7d8c-dd49-4d11-9379-3d2d6ab2d67e> ;
    ns1:city "Karlsruhe" ;
    ns1:country "Germany" ;
    ns1:house-number "26" ;
    ns1:postal-code "76133" ;
    ns1:street-name "Kari-Friedrich Straßeate" .

<https://example.com/73a9aab9-5599-4056-b3cd-40ab314eb13b> a ns2:Address ;
    ns2:hasBuilding <https://example.com/a01e6e44-48b0-41c4-aa7f-2fa3298e0f4c> ;
    ns1:city "Köln" ;
    ns1:country "Germany" ;
    ns1:house-number "6-8" ;
    ns1:postal-code "50668" ;
    ns1:street-name "An den Dominikanern" .

<https://example.com/7643f508-bea8-49c4-b403-ff72b62921bb> a ns2:Address ;
    ns2:hasBuilding <https://example.com/cccc8903-cbd3-4636-b0ea-992c16c004d3> ;
    ns1:city "Wien" ;
    ns1:country "Austria" ;
    ns1:house-number "1982" ;
    ns1:postal-code "1190" ;
    ns1:street-name "Knoten NuBdor Tragwerk" .

<https://example.com/7696f6fa-4117-4c41-aa92-3b1a9b52d852> a ns2:Address ;
    ns2:hasBuilding <https://example.com/b51655df-18bc-4b4f-b8a6-48337c68757d> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "126-128" ;
    ns1:postal-code "60314" ;
    ns1:street-name "Hanauer Landstrae" .

<https://example.com/76b5860b-ad34-44ca-909f-15d905d9e8fc> a ns2:Address ;
    ns2:hasBuilding <https://example.com/d86edecc-2881-4d9e-9372-faa829b541a6> ;
    ns1:city "Raunheim" ;
    ns1:country "Germany" ;
    ns1:house-number "2" ;
    ns1:postal-code "65479" ;
    ns1:street-name "Am Prime-Pare" .

<https://example.com/77924ad2-527e-4745-9c45-ce51e1b164af> a ns2:Address ;
    ns2:hasBuilding <https://example.com/f4c3b7c3-eb91-44f3-bce1-ec2903ea58df> ;
    ns1:city " Castel San Giovanni PC" ;
    ns1:country "Italy" ;
    ns1:house-number "2a" ;
    ns1:postal-code "29015" ;
    ns1:street-name "Via Dogana Po" .

<https://example.com/77f07fbc-e2c3-438e-b72a-ce4bb3972a5d> a ns2:Address ;
    ns2:hasBuilding <https://example.com/8c8e19d6-5c88-49f1-93a1-d16c78b942cf> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "12" ;
    ns1:postal-code "80335" ;
    ns1:street-name "Bayerstrae" .

<https://example.com/788c3fef-e497-4d68-9111-f5eb2c63e69c> a ns2:Address ;
    ns2:hasBuilding <https://example.com/bcb2188b-0ce9-4ec2-8d5c-c9131e302091> ;
    ns1:city "Barcelona" ;
    ns1:country "Spain" ;
    ns1:postal-code "08001" ;
    ns1:street-name "C de Pelai" .

<https://example.com/7893c76e-eefd-4bd4-8ada-e7982d37508f> a ns2:Address ;
    ns2:hasBuilding <https://example.com/f978168a-f8c2-44de-a827-ffead32b8cba> ;
    ns1:city "XP Den Haag" ;
    ns1:country "Netherlands" ;
    ns1:postal-code "2515" ;
    ns1:street-name "armassuspleinS" .

<https://example.com/7b172331-43e3-4351-9675-3067b19aae30> a ns2:Address ;
    ns2:hasBuilding <https://example.com/b5f9f964-0241-4db9-9ca1-2fc469650560> ;
    ns1:city "London " ;
    ns1:country "UK" ;
    ns1:house-number "34-36" ;
    ns1:postal-code "W1G 0JE" ;
    ns1:street-name "Margaret St" .

<https://example.com/7c1f5556-2aa4-420f-a504-dd1b2ed9984a> a ns2:Address ;
    ns2:hasBuilding <https://example.com/c7848807-75bd-4dc2-8bec-9df8c7949fe2> ;
    ns1:city "Nürnberg" ;
    ns1:country "Germany" ;
    ns1:house-number "32-36" ;
    ns1:postal-code "90402" ;
    ns1:street-name "Karolinenstraße" .

<https://example.com/7d033169-6efc-4f1a-8128-20e1bdfece9c> a ns2:Address ;
    ns2:hasBuilding <https://example.com/5f3672ed-fdea-4db2-b4cf-d06f9327d474> ;
    ns1:city "45 Kozomín" ;
    ns1:country "Czechia" ;
    ns1:postal-code "277" ;
    ns1:street-name "Budapest" .

<https://example.com/7d2c59cf-1cae-4f50-b137-ac79103499da> a ns2:Address ;
    ns2:hasBuilding <https://example.com/d63aaff1-e5c0-4fd8-b44d-5816efff4582> ;
    ns1:city "Hamburg" ;
    ns1:country "Germany" ;
    ns1:house-number "84" ;
    ns1:postal-code "20354" ;
    ns1:street-name "Never Wall" .

<https://example.com/7dfc816c-cce4-4dd7-9a89-c2c0a04731f1> a ns2:Address ;
    ns2:hasBuilding <https://example.com/ad8f9f50-c667-45e6-ac48-ac8d7ed7b29b> ;
    ns1:city "Berlin" ;
    ns1:country "Germany" ;
    ns1:house-number "2" ;
    ns1:postal-code "10178" ;
    ns1:street-name "Anna-Louisa-Karsch Straßeate" .

<https://example.com/7fe74459-5abc-42f4-99c4-d51f6eb05c97> a ns2:Address ;
    ns2:hasBuilding <https://example.com/1f1024af-c0f0-44f6-881b-77dae01bbd6a> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "571" ;
    ns1:postal-code "60549" ;
    ns1:street-name "Cargo City Sud Gebiude" .

<https://example.com/7ff07085-d6c9-4e05-8a75-9f609aa16c58> a ns2:Address ;
    ns2:hasBuilding <https://example.com/65c6d94e-ba3f-4c7e-8c49-942e1712cf1e> ;
    ns1:city "Düsseldorf" ;
    ns1:country "Germany" ;
    ns1:house-number "18-20" ;
    ns1:postal-code "40213" ;
    ns1:street-name "Benrather Straßeaße" .

<https://example.com/8036cf7f-0f2e-4e72-9585-2a0213cc4433> a ns2:Address ;
    ns2:hasBuilding <https://example.com/11f9f028-6ee1-495b-9978-3e1a23d7c3fb> ;
    ns1:city "Stuttgart" ;
    ns1:country "Germany" ;
    ns1:house-number "14" ;
    ns1:postal-code "70173" ;
    ns1:street-name "KonigstraBe" .

<https://example.com/8342681e-bfbb-4315-8798-908dc9b5f2e9> a ns2:Address ;
    ns2:hasBuilding <https://example.com/58929013-f385-45cc-83e6-bbb1b329818f> ;
    ns1:city "Bruxelles" ;
    ns1:country "Belgium" ;
    ns1:house-number "107" ;
    ns1:postal-code "1040" ;
    ns1:street-name "Rue dela Lol" .

<https://example.com/86b89036-0cca-4c25-9aa2-7a0aa670f9b0> a ns2:Address ;
    ns2:hasBuilding <https://example.com/b2912904-1a1e-4f3e-8a85-cc98d196c385> ;
    ns1:city "Issy-les-Moulineaux" ;
    ns1:country "France" ;
    ns1:house-number "26" ;
    ns1:postal-code "92130" ;
    ns1:street-name "Rue Hens Farman" .

<https://example.com/88d9f843-a782-41db-a5c6-e909410cc4d6> a ns2:Address ;
    ns2:hasBuilding <https://example.com/5154cc9b-6a58-4bb4-a015-7bad385f7c0c> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "2" ;
    ns1:postal-code "80336" ;
    ns1:street-name "Goethestrage" .

<https://example.com/93b5c1ab-31c4-40e3-aa9d-5c24b203f420> a ns2:Address ;
    ns2:hasBuilding <https://example.com/b0a3f8cc-829e-4fcd-bcbc-1b1db8d55bb0> ;
    ns1:city "Wien" ;
    ns1:country "Austria" ;
    ns1:house-number "7/27.06" ;
    ns1:postal-code "1220" ;
    ns1:street-name "Donau-City trae" .

<https://example.com/955aafb7-4870-42bd-b447-1fe6ced2e879> a ns2:Address ;
    ns2:hasBuilding <https://example.com/fe7d490e-2a8c-4329-8c6c-e413e0969bc4> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "558" ;
    ns1:postal-code "60549" ;
    ns1:street-name "Cargo City Sud Gebaude" .

<https://example.com/99f94589-009f-4322-b708-2ef93c4431c6> a ns2:Address ;
    ns2:hasBuilding <https://example.com/a02777cc-ae87-43f5-8a32-87d522d24d00> ;
    ns1:city "Pontenure PC" ;
    ns1:country "Italy" ;
    ns1:house-number "2" ;
    ns1:postal-code "29010" ;
    ns1:street-name "Via samu Amada" .

<https://example.com/9b6472d3-93ae-4dd2-a2a8-2030afc8ed1a> a ns2:Address ;
    ns2:hasBuilding <https://example.com/2c5ac2b6-7887-4b38-9203-c94df0e68b67> ;
    ns1:city "Kelsterbach" ;
    ns1:country "Germany" ;
    ns1:house-number "2" ;
    ns1:postal-code "65451" ;
    ns1:street-name "Donaustraße" .

<https://example.com/9d82f641-36f6-465c-9b5b-947abd3ed1d3> a ns2:Address ;
    ns2:hasBuilding <https://example.com/0fd04dd8-da57-4c66-a5b8-ecb693f833f4> ;
    ns1:city "Hamburg" ;
    ns1:country "Germany" ;
    ns1:house-number "14" ;
    ns1:postal-code "22767" ;
    ns1:street-name "GroBe Elostraße" .

<https://example.com/9faf0eca-cfff-4933-a8c9-52a2cfc706d7> a ns2:Address ;
    ns2:hasBuilding <https://example.com/67c98538-4b7c-421c-a9af-6efc3b2b21df> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "35-37" ;
    ns1:postal-code "60327" ;
    ns1:street-name "Friedrich Ebert-Anlage" .

<https://example.com/a06ce1f9-d666-48fa-b693-b3beefd98ddd> a ns2:Address ;
    ns2:hasBuilding <https://example.com/e2d82b02-76d0-4ceb-9df8-5c6293a333a6> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "77" ;
    ns1:postal-code "80339" ;
    ns1:street-name "Thereslenhohe" .

<https://example.com/a3408b13-da13-44c7-af5b-4353f5491be8> a ns2:Address ;
    ns2:hasBuilding <https://example.com/c82e04a6-352d-4b11-8fa8-1ee6d861973e> ;
    ns1:city "Issy-les-Moulineaux" ;
    ns1:country "France" ;
    ns1:house-number "9-15" ;
    ns1:postal-code "92130" ;
    ns1:street-name "Rue Maurice Mallet" .

<https://example.com/a7168c52-d954-4b0d-bc32-aed52b494de4> a ns2:Address ;
    ns2:hasBuilding <https://example.com/96c6d1fb-8146-42fc-bbf1-5ac0f10926c7> ;
    ns1:city "London " ;
    ns1:country "UK" ;
    ns1:house-number "197" ;
    ns1:postal-code "SE1 8JZ" ;
    ns1:street-name "Palestra House Blackfriars Ré" .

<https://example.com/a72447d7-8f89-416a-a7df-fb025553b122> a ns2:Address ;
    ns2:hasBuilding <https://example.com/2e503f44-0591-486c-b333-f326ed980a21> ;
    ns1:city "Wrocław" ;
    ns1:country "Poland" ;
    ns1:house-number "2" ;
    ns1:postal-code "50-265" ;
    ns1:street-name "Generata Jévefa Bema" .

<https://example.com/a9ebd1a5-73a4-4e41-bf62-81299f25b959> a ns2:Address ;
    ns2:hasBuilding <https://example.com/2042576d-5152-49bd-9785-8b8f4ee75d7d> ;
    ns1:city "Helsinki" ;
    ns1:country "Finland" ;
    ns1:house-number "13," ;
    ns1:postal-code "00120" ;
    ns1:street-name "Yrjonlatu" .

<https://example.com/aa90da7f-a054-4440-987d-b54aedd519e5> a ns2:Address ;
    ns2:hasBuilding <https://example.com/9bdb9a60-6beb-40da-82cd-d0f8b556ce9d> ;
    ns1:city "Braunschweig" ;
    ns1:country "Germany" ;
    ns1:house-number "1" ;
    ns1:postal-code "38100" ;
    ns1:street-name "Ritterbrunnen" .

<https://example.com/aadde3f1-ec26-4962-85b6-7e38dcb4875c> a ns2:Address ;
    ns2:hasBuilding <https://example.com/a451ae80-eb47-4842-a66c-5340406fe0d9> ;
    ns1:city "Bertrange" ;
    ns1:country "Luxembourg" ;
    ns1:house-number "23" ;
    ns1:postal-code "8070" ;
    ns1:street-name "Rue du pults Romain" .

<https://example.com/ad1c0f03-9827-45dd-a93a-2a0732e0af2f> a ns2:Address ;
    ns2:hasBuilding <https://example.com/9be56927-0ba1-4397-b8e2-db496d7528f8> ;
    ns1:city "London " ;
    ns1:country "UK" ;
    ns1:house-number "138" ;
    ns1:postal-code "EC3A 7AW" ;
    ns1:street-name "Houndsditch" .

<https://example.com/ae3ccd79-1850-4ef2-b9ef-fab0b13bf95b> a ns2:Address ;
    ns2:hasBuilding <https://example.com/ccc75006-4473-44b9-8776-f4bd971be866> ;
    ns1:city "Etterbeek" ;
    ns1:country "Belgium" ;
    ns1:house-number "214" ;
    ns1:postal-code "1040" ;
    ns1:street-name "Ousergemlaan" .

<https://example.com/aeb649e7-a519-44d8-939f-e7210a2c5cba> a ns2:Address ;
    ns2:hasBuilding <https://example.com/8c5f61ab-40f1-4aac-9d42-f5c3abff7b6f> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "558" ;
    ns1:postal-code "60549" ;
    ns1:street-name "Cargo City Sud Gebaude" .

<https://example.com/afbeaae2-a451-4f0a-a7d2-615f48f3b2a6> a ns2:Address ;
    ns2:hasBuilding <https://example.com/66ef33d4-3f94-4a03-8f2e-6d9ec801ae53> ;
    ns1:city "London " ;
    ns1:country "UK" ;
    ns1:house-number "5" ;
    ns1:postal-code "EC2V 7BP" ;
    ns1:street-name "Aldermanbury Barbican" .

<https://example.com/b203841a-89ff-4596-a53c-826c32ab1d9f> a ns2:Address ;
    ns2:hasBuilding <https://example.com/87389579-0188-4e4b-af64-10a14fce29f2> ;
    ns1:city "Wien" ;
    ns1:country "Austria" ;
    ns1:house-number "6" ;
    ns1:postal-code "1020" ;
    ns1:street-name "Trabrennstraße" .

<https://example.com/b26ca573-f10b-4568-acfb-63a430ea1e94> a ns2:Address ;
    ns2:hasBuilding <https://example.com/e9c0ca5b-02e8-4732-8c7d-86632baba8c5> ;
    ns1:city "Barcelona" ;
    ns1:country "Spain" ;
    ns1:house-number "6,8," ;
    ns1:postal-code "08001" ;
    ns1:street-name "C/ del Pintor Fortuny" .

<https://example.com/b441a9c9-c9d1-418d-a5e8-a1a1e8550235> a ns2:Address ;
    ns2:hasBuilding <https://example.com/46423443-e41a-487d-a07a-c7a9fd5e1ff4> ;
    ns1:city "Düsseldorf" ;
    ns1:country "Germany" ;
    ns1:house-number "3504" ;
    ns1:postal-code "40474" ;
    ns1:street-name "Alte FlughafenstraBe" .

<https://example.com/b491d9b2-c2e6-419c-a6e6-1ab7c91f1dda> a ns2:Address ;
    ns2:hasBuilding <https://example.com/dfcf1ea1-69bb-416d-a393-1ca5cd58aa5a> ;
    ns1:city "Barcelona" ;
    ns1:country "Spain" ;
    ns1:postal-code "Alta Diagonal, Av. Diagonal, 640, 6º A" ;
    ns1:street-name "Alta Diagonal" .

<https://example.com/b57b570f-8710-434f-b2f3-eaa809241db0> a ns2:Address ;
    ns2:hasBuilding <https://example.com/be0f3b3e-c7ed-4178-8cc4-6114ad605936> ;
    ns1:city "Berlin" ;
    ns1:country "Germany" ;
    ns1:house-number "52" ;
    ns1:postal-code "10117" ;
    ns1:street-name "Reinhardtstraße" .

<https://example.com/b92396d2-7746-4991-a577-1305fc5a2eb1> a ns2:Address ;
    ns2:hasBuilding <https://example.com/10bd9e8d-dc9f-4cd9-a131-045a9c596498> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "10" ;
    ns1:postal-code "81373" ;
    ns1:street-name "Landaubogen" .

<https://example.com/b9cca905-ab1c-4958-b3ed-bc37857afa44> a ns2:Address ;
    ns2:hasBuilding <https://example.com/cfa4612e-4f03-4e50-a56f-6111375b2ab5> ;
    ns1:city "Hamburg" ;
    ns1:country "Germany" ;
    ns1:house-number "29-32" ;
    ns1:postal-code "20354" ;
    ns1:street-name "Dammtorstrafe" .

<https://example.com/bab25ed5-5a81-44c9-83d6-eaabcb196b73> a ns2:Address ;
    ns2:hasBuilding <https://example.com/5b7a76cf-3c73-4553-9c30-5d3320084283> ;
    ns1:city "Castel San Giovanni PC" ;
    ns1:country "Italy" ;
    ns1:house-number "2a" ;
    ns1:postal-code "29015" ;
    ns1:street-name "Via Dogana Po" .

<https://example.com/bac81893-ffc3-44cb-9de1-de34ff78cb73> a ns2:Address ;
    ns2:hasBuilding <https://example.com/a96f509a-1ce8-4a30-9e24-0144bd202cf2> ;
    ns1:city "AD Amsterdam" ;
    ns1:country "Netherlands" ;
    ns1:house-number "59-72" ;
    ns1:postal-code "1012" ;
    ns1:street-name "Prins Hendrikkade" .

<https://example.com/bb18b414-c15f-4df6-84c1-53282d7d0d34> a ns2:Address ;
    ns2:hasBuilding <https://example.com/654b86d9-c44b-406a-a627-fdfaa29c317d> ;
    ns1:city "70 Postřižín-Odolena Voda" ;
    ns1:country "Czechia" ;
    ns1:house-number "173" ;
    ns1:postal-code "250" ;
    ns1:street-name "Vietoria Line" .

<https://example.com/bcd00c07-4e85-428e-9d9c-0cba6b3f2a1c> a ns2:Address ;
    ns2:hasBuilding <https://example.com/8144ad9f-a7f8-48a4-ac31-732aa7ea02b9> ;
    ns1:city "Magenta MI" ;
    ns1:country "Italy" ;
    ns1:house-number "98/2" ;
    ns1:postal-code "20013" ;
    ns1:street-name "Via Roma" .

<https://example.com/bf0bf34a-a3a2-4616-a0f7-811a11641cc2> a ns2:Address ;
    ns2:hasBuilding <https://example.com/57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "12" ;
    ns1:postal-code "80339" ;
    ns1:street-name "Barthstraße" .

<https://example.com/c00a2afa-7750-402f-a2b0-14fdbf69e046> a ns2:Address ;
    ns2:hasBuilding <https://example.com/a4288287-712a-4c30-86f5-26f4c9b39200> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "21/V" ;
    ns1:postal-code "80335" ;
    ns1:street-name "Bayerstraße" .

<https://example.com/c237e5e3-9476-410b-b793-321a7f912fa3> a ns2:Address ;
    ns2:hasBuilding <https://example.com/975306b0-55ed-4bec-8d1d-eb7403a01369> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "120" ;
    ns1:postal-code "75008" ;
    ns1:street-name "Rue du Faubourg Saint-Honoré" .

<https://example.com/c58327a2-b70f-4808-9be6-f3f61c45a367> a ns2:Address ;
    ns2:hasBuilding <https://example.com/075bf17a-11cc-4609-83ed-5b172ae187f2> ;
    ns1:city "Köln" ;
    ns1:country "Germany" ;
    ns1:house-number "11" ;
    ns1:postal-code "50672" ;
    ns1:street-name "Magnusstrafe" .

<https://example.com/c8baecf2-ab5f-44c3-b3b9-3251dbb049db> a ns2:Address ;
    ns2:hasBuilding <https://example.com/bb24716d-c0d1-4772-96f7-0a69002d759f> ;
    ns1:city "Poznań" ;
    ns1:country "Poland" ;
    ns1:postal-code "61-894" .

<https://example.com/c9112f6e-7cc0-4164-bf33-dc5cb6850981> a ns2:Address ;
    ns2:hasBuilding <https://example.com/0b8245dd-551c-45a4-bfc4-473e89ccacb5> ;
    ns1:city "Brussel" ;
    ns1:country "Belgium" ;
    ns1:house-number "12" ;
    ns1:postal-code "1000" ;
    ns1:street-name "Bischoffsheimlaan" .

<https://example.com/ca9bdc08-755d-42fc-b487-741a72c853e2> a ns2:Address ;
    ns2:hasBuilding <https://example.com/cbde8030-0835-401a-81bb-239aa0923056> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "19" ;
    ns1:postal-code "75008" ;
    ns1:street-name "Rue de Vienne" .

<https://example.com/d036555b-4752-44a8-bad4-b4bdf9c3c6b5> a ns2:Address ;
    ns2:hasBuilding <https://example.com/b663e664-9bcb-4adb-afb0-b3f7118ecec5> ;
    ns1:city "Berlin" ;
    ns1:country "Germany" ;
    ns1:house-number "106-108" ;
    ns1:postal-code "10623" ;
    ns1:street-name "Straße des Juni" .

<https://example.com/d3b37ffb-44f7-4cd3-8da7-22fa34417eb8> a ns2:Address ;
    ns2:hasBuilding <https://example.com/370d688f-fbac-4b18-9444-8ee31e05bb68> ;
    ns1:city "SC Venlo" ;
    ns1:country "Netherlands" ;
    ns1:house-number "2" ;
    ns1:postal-code "5928" ;
    ns1:street-name "Logistiekweg" .

<https://example.com/d5b4ed3a-a1b5-4987-a0b1-1804babf924a> a ns2:Address ;
    ns2:hasBuilding <https://example.com/deeddaa1-108b-4c09-a75d-5d867249b53f> ;
    ns1:city "Barcelona" ;
    ns1:country "Spain" ;
    ns1:house-number "443" ;
    ns1:postal-code "08002" ;
    ns1:street-name "PI de Catalunya Planta Oficina" .

<https://example.com/d772cb79-74f8-42e1-b4a1-0998b73fa0d0> a ns2:Address ;
    ns2:hasBuilding <https://example.com/5b191f8c-9775-4bd1-9d92-c875100fd773> ;
    ns1:city "DL Amsterdam" ;
    ns1:country "Netherlands" ;
    ns1:house-number "163" ;
    ns1:postal-code "1011" ;
    ns1:street-name "Oosterdokskade" .

<https://example.com/d8723f3d-dc77-4539-b854-531c488a12af> a ns2:Address ;
    ns2:hasBuilding <https://example.com/2ce6a300-22c3-4754-a5c4-6d5630632546> ;
    ns1:city "Milano MI" ;
    ns1:country "Italy" ;
    ns1:house-number "16" ;
    ns1:postal-code "20121" ;
    ns1:street-name "Via Broletto" .

<https://example.com/d965425e-fb4e-4c22-98c2-477c20f5bac1> a ns2:Address ;
    ns2:hasBuilding <https://example.com/40c031cd-6838-44e2-bd5f-8509fb2ff226> ;
    ns1:city "45 Kozomín" ;
    ns1:country "Czechia" ;
    ns1:house-number "6" ;
    ns1:postal-code "277" ;
    ns1:street-name "Stortingsgata" .

<https://example.com/db63f613-1f33-43e4-aeb0-b1e41c5bbb5d> a ns2:Address ;
    ns2:hasBuilding <https://example.com/2df597b6-5476-42ef-8aad-7796870ca628> ;
    ns1:city "Edinburgh " ;
    ns1:country "UK" ;
    ns1:house-number "144" ;
    ns1:postal-code "EH3 8EX" ;
    ns1:street-name "Atria One Morrison St" .

<https://example.com/dd002b44-8118-4469-8cc5-1db5bcf63c0f> a ns2:Address ;
    ns2:hasBuilding <https://example.com/858414b0-9520-4d68-afac-b856688eb3d7> ;
    ns1:city "Kelsterbach" ;
    ns1:country "Germany" ;
    ns1:house-number "1" ;
    ns1:postal-code "65451" ;
    ns1:street-name "Spreestraße" .

<https://example.com/dd4bb631-939a-45c0-a766-19c050c115af> a ns2:Address ;
    ns2:hasBuilding <https://example.com/2b5382c5-80cc-4151-a607-38c4a68ed3f7> ;
    ns1:city "London " ;
    ns1:country "UK" ;
    ns1:house-number "3" ;
    ns1:postal-code "SW1Y 4JU" ;
    ns1:street-name "pl. Wiadystawa Andersa" .

<https://example.com/dd56c51f-8d9b-4a1a-bffc-9c3386c2d868> a ns2:Address ;
    ns2:hasBuilding <https://example.com/b4d50434-46d9-4bd1-b7fd-c9336e157401> ;
    ns1:city "CE Amsterdam" ;
    ns1:country "Netherlands" ;
    ns1:house-number "595" ;
    ns1:postal-code "1017" ;
    ns1:street-name "Herengracht" .

<https://example.com/df4eccb4-8f5c-4cf5-8365-0e3c228c4ffe> a ns2:Address ;
    ns2:hasBuilding <https://example.com/6358540d-a6cf-4928-a3a9-05f7ed43f408> ;
    ns1:city "Neudorf-Weimershof " ;
    ns1:country "Luxembourg" ;
    ns1:house-number "38" ;
    ns1:postal-code "1855" ;
    ns1:street-name "Av. John F. Kennedy" .

<https://example.com/e2ed1add-caca-4511-95d1-5b928f7c3059> a ns2:Address ;
    ns2:hasBuilding <https://example.com/89afdf02-d465-4814-85ec-9ac00ef4c580> ;
    ns1:city "Raunheim" ;
    ns1:country "Germany" ;
    ns1:house-number "1" ;
    ns1:postal-code "65479" ;
    ns1:street-name "Stockstrage" .

<https://example.com/e307b8cf-9975-4889-bba5-36524843e3c3> a ns2:Address ;
    ns2:hasBuilding <https://example.com/d5ccdae6-a1b9-4d9c-8657-00aa64c5d7a8> ;
    ns1:city "Warszawa" ;
    ns1:country "Poland" ;
    ns1:postal-code "00-622" .

<https://example.com/e3784a8d-53fb-44d0-b714-c832770b708f> a ns2:Address ;
    ns2:hasBuilding <https://example.com/7111ddf7-10b7-4a6b-8ad9-8beccef16f10> ;
    ns1:city "Wien" ;
    ns1:country "Austria" ;
    ns1:house-number "13" ;
    ns1:postal-code "1010" ;
    ns1:street-name "Opernving" .

<https://example.com/ef0b2a11-9e96-4426-bf4d-8b069bc5db54> a ns2:Address ;
    ns2:hasBuilding <https://example.com/da857556-b790-4f2c-b193-523c29501fe1> ;
    ns1:city "Frankfurt am Main" ;
    ns1:country "Germany" ;
    ns1:house-number "9" ;
    ns1:postal-code "60311" ;
    ns1:street-name "Grofe Gallusstraße" .

<https://example.com/f1901c05-ebe4-4109-b0b4-540e84878344> a ns2:Address ;
    ns2:hasBuilding <https://example.com/e20a7cde-b6df-4ec1-9c21-00cb619c66a6> ;
    ns1:city "LS Amsterdam" ;
    ns1:country "Netherlands" ;
    ns1:house-number "14" ;
    ns1:postal-code "1082" ;
    ns1:street-name "Gustav Malerlaan" .

<https://example.com/f4881573-ad13-4d33-a0c4-2a9de37a4d4d> a ns2:Address ;
    ns2:hasBuilding <https://example.com/dfa528aa-af9f-48bd-b0a6-dd7320157c8b> ;
    ns1:city "Paris" ;
    ns1:country "France" ;
    ns1:house-number "54" ;
    ns1:postal-code "75009" ;
    ns1:street-name "Bd Haussmann" .

<https://example.com/fb06d8da-e554-4132-b1fd-00462f3ec118> a ns2:Address ;
    ns2:hasBuilding <https://example.com/444f8198-cfe9-4eec-ae72-f690224d8881> ;
    ns1:city "Neuilly-sur-Seine" ;
    ns1:country "France" ;
    ns1:house-number "1" ;
    ns1:postal-code "92200" ;
    ns1:street-name "Rue des Hussiers" .

<https://example.com/fc4c1b2b-349d-4e65-a9dd-4e6ecbfb6037> a ns2:Address ;
    ns2:hasBuilding <https://example.com/3942f7e3-440b-47b2-9c0a-587c5543c71e> ;
    ns1:city "Köln" ;
    ns1:country "Germany" ;
    ns1:house-number "2-4" ;
    ns1:postal-code "50667" ;
    ns1:street-name "Neumarkt" .

<https://example.com/fc6678d2-3ac4-4353-87ae-4dadb8c02d15> a ns2:Address ;
    ns2:hasBuilding <https://example.com/97b5f5d4-eebf-4874-b9f5-83bbf9f106fa> ;
    ns1:city "San Nazzaro PC" ;
    ns1:country "Italy" ;
    ns1:house-number "1" ;
    ns1:postal-code "29010" ;
    ns1:street-name "Via Enrico Mattei" .

<https://example.com/fcbc08f6-9fb1-4273-be1e-48045f388881> a ns2:Address ;
    ns2:hasBuilding <https://example.com/ec3dc6d1-f6ef-4099-909f-ab897b7583ac> ;
    ns1:city "London " ;
    ns1:country "UK" ;
    ns1:postal-code "W1T 1FB" ;
    ns1:street-name "Greybowska SA" .

<https://example.com/fcde3cfb-9806-48b3-99c8-34f08dda3dc7> a ns2:Address ;
    ns2:hasBuilding <https://example.com/b0d2d212-2722-4e01-a3c2-721dc3eaed93> ;
    ns1:city "London " ;
    ns1:country "UK" ;
    ns1:house-number "3" ;
    ns1:postal-code "SW1E 5ER" ;
    ns1:street-name "Upowa" .

<https://example.com/fd3e3af3-a573-4a90-8910-1658cbf7be5d> a ns2:Address ;
    ns2:hasBuilding <https://example.com/763d5362-1d55-4319-8832-f931e89dd3d8> ;
    ns1:city "München" ;
    ns1:country "Germany" ;
    ns1:house-number "$9" ;
    ns1:postal-code "80636" ;
    ns1:street-name "Arnulfstrae" .

<https://example.com/ff24a9bc-6ab0-40cd-9c66-82a624ef3ea6> a ns2:Address ;
    ns2:hasBuilding <https://example.com/a3251601-d4a3-4345-ba97-c7cf47062948> ;
    ns1:city "Berlin" ;
    ns1:country "Germany" ;
    ns1:house-number "49" ;
    ns1:postal-code "10119" ;
    ns1:street-name "Torstraße" .

<https://example.com/0058c837-25b9-4345-9cc1-ec056f456052> a ns2:Building ;
    ns1:building-code "********94" ;
    ns1:construction-year "2009" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "PlusZwei" ;
    ns1:parking-spaces "430" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/06ccb9e5-e34b-4637-aaa4-d5e345cc7d22> a ns2:Building ;
    ns1:building-code "********21" ;
    ns1:construction-year "1986" ;
    ns1:energy-efficiency-class "E" ;
    ns1:name "Poseidon" ;
    ns1:parking-spaces "432" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/075bf17a-11cc-4609-83ed-5b172ae187f2> a ns2:Building ;
    ns1:building-code "********17" ;
    ns1:construction-year "1999" ;
    ns1:energy-efficiency-class "A" ;
    ns1:name "Magnusstraße 11" ;
    ns1:parking-spaces "149" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/0a2add59-d7d2-4802-ac6d-2bbe4f1a4575> a ns2:Building ;
    ns1:building-code "1000000135" ;
    ns1:construction-year "2007" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Forum Gliwice" ;
    ns1:parking-spaces "823" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/0a8cbe14-3d18-4c6c-a778-abade5b4b8b1> a ns2:Building ;
    ns1:building-code "********99" ;
    ns1:construction-year "2018" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Général Foy" ;
    ns1:parking-spaces "79" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/0b3dfe7c-08fd-4139-8fc4-f99d963cd23c> a ns2:Building ;
    ns1:building-code "1000000132" ;
    ns1:construction-year "1998" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Atrinova" ;
    ns1:parking-spaces "110" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/0b8245dd-551c-45a4-bfc4-473e89ccacb5> a ns2:Building ;
    ns1:building-code "********92" ;
    ns1:construction-year "2019" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Spectrum" ;
    ns1:parking-spaces "150" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/0fb819dd-8215-4224-be62-f8bc93aab237> a ns2:Building ;
    ns1:building-code "********74" ;
    ns1:construction-year "1932" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "New Deal" ;
    ns1:parking-spaces "165" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/0fd04dd8-da57-4c66-a5b8-ecb693f833f4> a ns2:Building ;
    ns1:building-code "********34" ;
    ns1:construction-year "1966" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Große Elbstraße 14" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/10bd9e8d-dc9f-4cd9-a131-045a9c596498> a ns2:Building ;
    ns1:building-code "********28" ;
    ns1:construction-year "1990" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "Alpha-Haus" ;
    ns1:parking-spaces "291" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/10d7c8c3-bc2f-4166-97d7-d11f04a39d82> a ns2:Building ;
    ns1:building-code "********02" ;
    ns1:construction-year "1999" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Checkpoint Charlie" ;
    ns1:parking-spaces "207" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/11f9f028-6ee1-495b-9978-3e1a23d7c3fb> a ns2:Building ;
    ns1:building-code "********26" ;
    ns1:construction-year "1925" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "Königstraße 14" ;
    ns1:parking-spaces "13" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/1892ebb9-9532-4dee-bddc-a7044da700a2> a ns2:Building ;
    ns1:building-code "********05" ;
    ns1:construction-year "2003" ;
    ns1:energy-efficiency-class "E" ;
    ns1:name "Atrium Plaza" ;
    ns1:parking-spaces "151" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/1ba5c96e-d666-4981-8962-b62d195f4eca> a ns2:Building ;
    ns1:building-code "********78" ;
    ns1:construction-year "1900" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "One Southampton Row" ;
    ns1:parking-spaces "6" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/1ccb4dde-ccc2-4267-aa90-d682090fe9ab> a ns2:Building ;
    ns1:building-code "********63" ;
    ns1:construction-year "1904" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Kaufhaus Gerngross" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/1f1024af-c0f0-44f6-881b-77dae01bbd6a> a ns2:Building ;
    ns1:building-code "********04" ;
    ns1:construction-year "2003" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "AIR CARGO Center" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Industrial, Distribution Warehouse" ;
    ns1:valid-from "44926" .

<https://example.com/2042576d-5152-49bd-9785-8b8f4ee75d7d> a ns2:Building ;
    ns1:building-code "1000000119" ;
    ns1:construction-year "1840" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Hotel St. George" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/21c44521-ea5e-4e8e-97ce-d67fd02eeac4> a ns2:Building ;
    ns1:building-code "1000000103" ;
    ns1:construction-year "1860" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "23 Opéra" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/2b5382c5-80cc-4151-a607-38c4a68ed3f7> a ns2:Building ;
    ns1:building-code "1000000129" ;
    ns1:construction-year "2015" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "8 St James's Square, St. James's," ;
    ns1:parking-spaces "6" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/2c5ac2b6-7887-4b38-9203-c94df0e68b67> a ns2:Building ;
    ns1:building-code "********46" ;
    ns1:construction-year "2017" ;
    ns1:energy-efficiency-class "A" ;
    ns1:name "Mönchhof Frankfurt Airport" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/2ce6a300-22c3-4754-a5c4-6d5630632546> a ns2:Building ;
    ns1:building-code "1000000112" ;
    ns1:construction-year "1871" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Via Broletto 16" ;
    ns1:parking-spaces "30" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/2df597b6-5476-42ef-8aad-7796870ca628> a ns2:Building ;
    ns1:building-code "********83" ;
    ns1:construction-year "2013" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Atria One & Two" ;
    ns1:parking-spaces "20" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/2e503f44-0591-486c-b333-f326ed980a21> a ns2:Building ;
    ns1:building-code "********85" ;
    ns1:construction-year "2007" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Bema Plaza" ;
    ns1:parking-spaces "397" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/319ebfe7-3d4a-4b71-9216-e65dfb668f81> a ns2:Building ;
    ns1:building-code "********66" ;
    ns1:construction-year "1900" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "C. de Carretas" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/370d688f-fbac-4b18-9444-8ee31e05bb68> a ns2:Building ;
    ns1:building-code "********58" ;
    ns1:construction-year "2020" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "vidaXL Phase 3" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/3942f7e3-440b-47b2-9c0a-587c5543c71e> a ns2:Building ;
    ns1:building-code "********42" ;
    ns1:construction-year "2013" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Neumarkt Galerie" ;
    ns1:parking-spaces "141" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/3c04623f-e668-40e8-81a8-1945d6e577d8> a ns2:Building ;
    ns1:building-code "********35" ;
    ns1:construction-year "2009" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Logistikzentrum Bremen" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Industrial, Distribution Warehouse" ;
    ns1:valid-from "44926" .

<https://example.com/40c031cd-6838-44e2-bd5f-8509fb2ff226> a ns2:Building ;
    ns1:building-code "1000000128" ;
    ns1:construction-year "2018" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "CTPark Prague North 2" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/42ee10cc-3dfa-496b-b165-8deeb57f63ea> a ns2:Building ;
    ns1:building-code "********36" ;
    ns1:construction-year "2008" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "Hohe Bleichen 11" ;
    ns1:parking-spaces "34" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/444f8198-cfe9-4eec-ae72-f690224d8881> a ns2:Building ;
    ns1:building-code "********73" ;
    ns1:construction-year "1972" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "1 Rue des Huissiers" ;
    ns1:parking-spaces "294" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/4620ead9-e050-4d4c-9b7b-d8bff52936b8> a ns2:Building ;
    ns1:building-code "********48" ;
    ns1:construction-year "2006" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "Das Schloss Shoppingcenter" ;
    ns1:parking-spaces "580" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/46423443-e41a-487d-a07a-c7a9fd5e1ff4> a ns2:Building ;
    ns1:building-code "********51" ;
    ns1:construction-year "2012" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "CUBES" ;
    ns1:parking-spaces "218" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/4e5596f9-f928-47a8-907f-2538d291e0be> a ns2:Building ;
    ns1:building-code "********77" ;
    ns1:construction-year "2005" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Moor House" ;
    ns1:parking-spaces "15" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/4f72a0ac-ef2f-4e33-aa1b-c34572988b6a> a ns2:Building ;
    ns1:building-code "********20" ;
    ns1:construction-year "1972" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Isenburg-Zentrum" ;
    ns1:parking-spaces "1530" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/5045a210-29bb-4b4e-b073-3026d291047d> a ns2:Building ;
    ns1:building-code "********23" ;
    ns1:construction-year "1992" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Mainzer Landstraße 293" ;
    ns1:parking-spaces "342" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/5154cc9b-6a58-4bb4-a015-7bad385f7c0c> a ns2:Building ;
    ns1:building-code "********44" ;
    ns1:construction-year "2002" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Hotel Le Méridien" ;
    ns1:parking-spaces "72" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/52ca00a9-2cbe-4f54-8ea8-7b9f62c29f5d> a ns2:Building ;
    ns1:building-code "********53" ;
    ns1:construction-year "2009" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Distribution Center Flextronics" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/57cd12c2-f87c-48cf-be65-66574c335774> a ns2:Building ;
    ns1:building-code "********07" ;
    ns1:construction-year "2002" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "s´Zentrum" ;
    ns1:parking-spaces "87" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2> a ns2:Building ;
    ns1:building-code "********01" ;
    ns1:construction-year "2001" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "RONDO" ;
    ns1:parking-spaces "324" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/58929013-f385-45cc-83e6-bbb1b329818f> a ns2:Building ;
    ns1:building-code "********91" ;
    ns1:construction-year "2019" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "The One" ;
    ns1:parking-spaces "148" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/5b191f8c-9775-4bd1-9d92-c875100fd773> a ns2:Building ;
    ns1:building-code "1000000120" ;
    ns1:construction-year "2022" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Oosterdokskade 163" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/5b7a76cf-3c73-4553-9c30-5d3320084283> a ns2:Building ;
    ns1:building-code "1000000106" ;
    ns1:construction-year "2005" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Logistic Park Castel San Giovanni" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Industrial, Distribution Warehouse" ;
    ns1:valid-from "44926" .

<https://example.com/5f3672ed-fdea-4db2-b4cf-d06f9327d474> a ns2:Building ;
    ns1:building-code "1000000127" ;
    ns1:construction-year "2010" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "CTPark Prague North 1" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/6358540d-a6cf-4928-a3a9-05f7ed43f408> a ns2:Building ;
    ns1:building-code "1000000113" ;
    ns1:construction-year "2002" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "38 Av. John F. Kennedy" ;
    ns1:parking-spaces "322" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/654b86d9-c44b-406a-a627-fdfaa29c317d> a ns2:Building ;
    ns1:building-code "1000000125" ;
    ns1:construction-year "2006" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Tesco Distribution Center" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/65c6d94e-ba3f-4c7e-8c49-942e1712cf1e> a ns2:Building ;
    ns1:building-code "********10" ;
    ns1:construction-year "2003" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "Benrather-Karree" ;
    ns1:parking-spaces "414" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/66c560f4-2f87-40af-9e3c-992d886a1d60> a ns2:Building ;
    ns1:building-code "1000000100" ;
    ns1:construction-year "2009" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Hotel Renaissance Arc de Triomphe" ;
    ns1:parking-spaces "38" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/66ef33d4-3f94-4a03-8f2e-6d9ec801ae53> a ns2:Building ;
    ns1:building-code "********80" ;
    ns1:construction-year "2007" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Aldermanbury Square" ;
    ns1:parking-spaces "18" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/67c98538-4b7c-421c-a9af-6efc3b2b21df> a ns2:Building ;
    ns1:building-code "1000000122" ;
    ns1:construction-year "2012" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Friedrich-Ebert-Anlage 35-37" ;
    ns1:parking-spaces "552" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/6ac5af7c-329c-47b1-82c0-e21c10454b38> a ns2:Building ;
    ns1:building-code "********13" ;
    ns1:construction-year "2000" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Prime Parc, Bauteil A1-A5" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/6edcfd7b-47ed-46f1-9a91-c3e99a082b92> a ns2:Building ;
    ns1:building-code "********90" ;
    ns1:construction-year "2000" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Boréal" ;
    ns1:parking-spaces "405" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/6f8a060f-0954-411a-aba3-0425fc60288c> a ns2:Building ;
    ns1:building-code "1000000117" ;
    ns1:construction-year "2005" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Mahon Point Shopping Center" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/7111ddf7-10b7-4a6b-8ad9-8beccef16f10> a ns2:Building ;
    ns1:building-code "********61" ;
    ns1:construction-year "2019" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Hotel \"Le Méridien\"" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/7310956d-6d8f-4bd7-af7b-4943aae4c2a4> a ns2:Building ;
    ns1:building-code "1000000124" ;
    ns1:construction-year "2008" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Gemini" ;
    ns1:parking-spaces "484" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/763d5362-1d55-4319-8832-f931e89dd3d8> a ns2:Building ;
    ns1:building-code "********37" ;
    ns1:construction-year "2010" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "Arnulfstraße 59" ;
    ns1:parking-spaces "210" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/76eb609f-9a49-4441-9258-a4cb9d63dcd1> a ns2:Building ;
    ns1:building-code "********67" ;
    ns1:construction-year "1989" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Calle Conde de Gondomar" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/7b46d5e9-cb1b-4897-bb20-14c4110629ab> a ns2:Building ;
    ns1:building-code "1000000116" ;
    ns1:construction-year "2006" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Whitewater" ;
    ns1:parking-spaces "1701" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/8144ad9f-a7f8-48a4-ac31-732aa7ea02b9> a ns2:Building ;
    ns1:building-code "1000000108" ;
    ns1:construction-year "2007" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Via Roma 98/a" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/832cb406-5337-4132-81b6-74298751d028> a ns2:Building ;
    ns1:building-code "********71" ;
    ns1:construction-year "1878" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Le Centorial" ;
    ns1:parking-spaces "267" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/858414b0-9520-4d68-afac-b856688eb3d7> a ns2:Building ;
    ns1:building-code "********49" ;
    ns1:construction-year "2020" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "Logistikzentrum FFM-Airport, 2. BA" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/87389579-0188-4e4b-af64-10a14fce29f2> a ns2:Building ;
    ns1:building-code "********93" ;
    ns1:construction-year "2009" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "HochZwei" ;
    ns1:parking-spaces "69" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/8995106d-e483-4266-a3c7-65120a6f3bbe> a ns2:Building ;
    ns1:building-code "********12" ;
    ns1:construction-year "1993" ;
    ns1:energy-efficiency-class "E" ;
    ns1:name "Fleethof" ;
    ns1:parking-spaces "236" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/899d7524-f048-4006-aeb8-7fc0410c05b4> a ns2:Building ;
    ns1:building-code "********27" ;
    ns1:construction-year "2003" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "Herriot" ;
    ns1:parking-spaces "673" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/89afdf02-d465-4814-85ec-9ac00ef4c580> a ns2:Building ;
    ns1:building-code "********14" ;
    ns1:construction-year "2001" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "Prime Parc, Bauteil B1-B8" ;
    ns1:parking-spaces "270" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/8c5f61ab-40f1-4aac-9d42-f5c3abff7b6f> a ns2:Building ;
    ns1:building-code "********32" ;
    ns1:construction-year "2000" ;
    ns1:energy-efficiency-class "E" ;
    ns1:name "FLZ – Fracht- und Logistik Zentrum" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Industrial, Distribution Warehouse" ;
    ns1:valid-from "44926" .

<https://example.com/8c8e19d6-5c88-49f1-93a1-d16c78b942cf> a ns2:Building ;
    ns1:building-code "********45" ;
    ns1:construction-year "2004" ;
    ns1:energy-efficiency-class "F" ;
    ns1:name "Sofitel Munich Bayerpost" ;
    ns1:parking-spaces "70" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/8e69a400-3087-4b3d-93a4-fcf0ba1dafd3> a ns2:Building ;
    ns1:building-code "********56" ;
    ns1:construction-year "1954" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Cool 63" ;
    ns1:parking-spaces "37" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/8e8d2928-9d12-4b2a-8e7e-5136c6dd736a> a ns2:Building ;
    ns1:building-code "1000000126" ;
    ns1:construction-year "2012" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "City Green Court" ;
    ns1:parking-spaces "236" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/8fa01b37-3d32-4a26-9aea-0852d3d4c191> a ns2:Building ;
    ns1:building-code "1000000123" ;
    ns1:construction-year "2006" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Rte des Acacias 60" ;
    ns1:parking-spaces "827" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/9003555b-57c5-426a-b8e5-31fcdc035e5b> a ns2:Building ;
    ns1:building-code "********76" ;
    ns1:construction-year "2008" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Old Jewry" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/90e3d68a-313d-401d-82cd-f03587ddda23> a ns2:Building ;
    ns1:building-code "1000000105" ;
    ns1:construction-year "1892" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Via Orefici 13" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/96c6d1fb-8146-42fc-bbf1-5ac0f10926c7> a ns2:Building ;
    ns1:building-code "********79" ;
    ns1:construction-year "2006" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Palestra" ;
    ns1:parking-spaces "30" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/975306b0-55ed-4bec-8d1d-eb7403a01369> a ns2:Building ;
    ns1:building-code "********70" ;
    ns1:construction-year "2018" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "120 Rue du Faubourg Saint-Honoré" ;
    ns1:parking-spaces "26" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/97b5f5d4-eebf-4874-b9f5-83bbf9f106fa> a ns2:Building ;
    ns1:building-code "1000000107" ;
    ns1:construction-year "2010" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Logistics Centre A/C" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Industrial, Distribution Warehouse" ;
    ns1:valid-from "44926" .

<https://example.com/986ee284-f90a-4d68-a0a1-7294fe92de88> a ns2:Building ;
    ns1:building-code "********30" ;
    ns1:construction-year "2009" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Logistikzentrum Rhein-Ruhr" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Industrial, Distribution Warehouse" ;
    ns1:valid-from "44926" .

<https://example.com/99120dc1-8ee9-46c3-b465-c7e890895a76> a ns2:Building ;
    ns1:building-code "********38" ;
    ns1:construction-year "2002" ;
    ns1:energy-efficiency-class "E" ;
    ns1:name "Friedrich Carré I" ;
    ns1:parking-spaces "201" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/9bdb9a60-6beb-40da-82cd-d0f8b556ce9d> a ns2:Building ;
    ns1:building-code "********39" ;
    ns1:construction-year "2007" ;
    ns1:energy-efficiency-class "F" ;
    ns1:name "Schloss-Arkaden" ;
    ns1:parking-spaces "1271" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/9be56927-0ba1-4397-b8e2-db496d7528f8> a ns2:Building ;
    ns1:building-code "********81" ;
    ns1:construction-year "2010" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "The St. Botolph Building" ;
    ns1:parking-spaces "14" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/a01e6e44-48b0-41c4-aa7f-2fa3298e0f4c> a ns2:Building ;
    ns1:building-code "********29" ;
    ns1:construction-year "2000" ;
    ns1:energy-efficiency-class "F" ;
    ns1:name "Lindner Hotel" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/a02777cc-ae87-43f5-8a32-87d522d24d00> a ns2:Building ;
    ns1:building-code "1000000109" ;
    ns1:construction-year "2011" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Logistics Centre B" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Industrial, Distribution Warehouse" ;
    ns1:valid-from "44926" .

<https://example.com/a2e0b5f8-b290-440f-bdfb-4861730dc59a> a ns2:Building ;
    ns1:building-code "********22" ;
    ns1:construction-year "1975" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Westend Sky" ;
    ns1:parking-spaces "122" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/a3251601-d4a3-4345-ba97-c7cf47062948> a ns2:Building ;
    ns1:building-code "********31" ;
    ns1:construction-year "1996" ;
    ns1:energy-efficiency-class "F" ;
    ns1:name "Schönhauser Tor" ;
    ns1:parking-spaces "124" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/a4288287-712a-4c30-86f5-26f4c9b39200> a ns2:Building ;
    ns1:building-code "********25" ;
    ns1:construction-year "1975" ;
    ns1:energy-efficiency-class "A" ;
    ns1:name "Bayerstraße 21/V" ;
    ns1:parking-spaces "24" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/a451ae80-eb47-4842-a66c-5340406fe0d9> a ns2:Building ;
    ns1:building-code "1000000114" ;
    ns1:construction-year "2011" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Vitrum" ;
    ns1:parking-spaces "540" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/a5271b50-54b9-4517-af00-c7c2d163a1f4> a ns2:Building ;
    ns1:building-code "1000000118" ;
    ns1:construction-year "2018" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Clayton Hotel Charlemont" ;
    ns1:parking-spaces "20" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/a8fa7d8c-dd49-4d11-9379-3d2d6ab2d67e> a ns2:Building ;
    ns1:building-code "********88" ;
    ns1:construction-year "2005" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Ettlinger Tor Karlsruhe" ;
    ns1:parking-spaces "846" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/a96f509a-1ce8-4a30-9e24-0144bd202cf2> a ns2:Building ;
    ns1:building-code "********55" ;
    ns1:construction-year "2016" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Hotel NH Collection Amsterdam" ;
    ns1:parking-spaces "26" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/ad8f9f50-c667-45e6-ac48-ac8d7ed7b29b> a ns2:Building ;
    ns1:building-code "********11" ;
    ns1:construction-year "2002" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "SpreePalais am Dom" ;
    ns1:parking-spaces "166" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/adbd1c9e-49b4-4f4d-8155-6329f72b6b6d> a ns2:Building ;
    ns1:building-code "1000000111" ;
    ns1:construction-year "2010" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "MAC567" ;
    ns1:parking-spaces "287" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/b0a3f8cc-829e-4fcd-bcbc-1b1db8d55bb0> a ns2:Building ;
    ns1:building-code "1000000121" ;
    ns1:construction-year "2013" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "DC Tower" ;
    ns1:parking-spaces "414" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/b0d2d212-2722-4e01-a3c2-721dc3eaed93> a ns2:Building ;
    ns1:building-code "1000000130" ;
    ns1:construction-year "1960" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "173 Victoria Line" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/b2912904-1a1e-4f3e-8a85-cc98d196c385> a ns2:Building ;
    ns1:building-code "**********" ;
    ns1:construction-year "2010" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "EQWATER" ;
    ns1:parking-spaces "220" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/b4d50434-46d9-4bd1-b7fd-c9336e157401> a ns2:Building ;
    ns1:building-code "********54" ;
    ns1:construction-year "1931" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "The Bank" ;
    ns1:parking-spaces "110" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/b51655df-18bc-4b4f-b8a6-48337c68757d> a ns2:Building ;
    ns1:building-code "********18" ;
    ns1:construction-year "1966" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Lighttower" ;
    ns1:parking-spaces "57" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/b5f9f964-0241-4db9-9ca1-2fc469650560> a ns2:Building ;
    ns1:building-code "**********" ;
    ns1:construction-year "2013" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Margaret Street 33" ;
    ns1:parking-spaces "110" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/b663e664-9bcb-4adb-afb0-b3f7118ecec5> a ns2:Building ;
    ns1:building-code "********08" ;
    ns1:construction-year "2005" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "Tiergarten Tower" ;
    ns1:parking-spaces "157" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/bb24716d-c0d1-4772-96f7-0a69002d759f> a ns2:Building ;
    ns1:building-code "1000000134" ;
    ns1:construction-year "2007" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Andersia Tower" ;
    ns1:parking-spaces "217" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/bcb2188b-0ce9-4ec2-8d5c-c9131e302091> a ns2:Building ;
    ns1:building-code "********68" ;
    ns1:construction-year "1936" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "C. de Pelai" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/be0f3b3e-c7ed-4178-8cc4-6114ad605936> a ns2:Building ;
    ns1:building-code "********03" ;
    ns1:construction-year "2003" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Waterfalls Berlin" ;
    ns1:parking-spaces "71" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/be3982d9-1086-4de2-a5be-614a95e13330> a ns2:Building ;
    ns1:building-code "********84" ;
    ns1:construction-year "2014" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "John Lewis MP2" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/c159db6c-d0a5-4298-baff-f65a2d735bcc> a ns2:Building ;
    ns1:building-code "********87" ;
    ns1:construction-year "2019" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Generation Park Z" ;
    ns1:parking-spaces "110" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/c2cb5e24-bff4-4bc9-aaba-4d6de75dce3a> a ns2:Building ;
    ns1:building-code "********86" ;
    ns1:construction-year "2004" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Tesco Distribution Center" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Industrial, Distribution Warehouse" ;
    ns1:valid-from "44926" .

<https://example.com/c592f971-2fda-4169-8e89-c7f296a3300f> a ns2:Building ;
    ns1:building-code "********15" ;
    ns1:construction-year "2004" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "Prime Parc, Bauteil C1 + Parkhaus" ;
    ns1:parking-spaces "283" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/c7848807-75bd-4dc2-8bec-9df8c7949fe2> a ns2:Building ;
    ns1:building-code "********06" ;
    ns1:construction-year "2003" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "Kaufhaus Breuninger" ;
    ns1:parking-spaces "107" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/c82e04a6-352d-4b11-8fa8-1ee6d861973e> a ns2:Building ;
    ns1:building-code "********98" ;
    ns1:construction-year "2000" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Central Park" ;
    ns1:parking-spaces "310" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/cbde8030-0835-401a-81bb-239aa0923056> a ns2:Building ;
    ns1:building-code "1000000101" ;
    ns1:construction-year "1911" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Solstys" ;
    ns1:parking-spaces "197" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/cc4e3285-f6a0-4e9b-a204-4a70b555ab25> a ns2:Building ;
    ns1:building-code "********24" ;
    ns1:construction-year "1922" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "LEOMAX" ;
    ns1:parking-spaces "32" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/ccc75006-4473-44b9-8776-f4bd971be866> a ns2:Building ;
    ns1:building-code "********60" ;
    ns1:construction-year "1996" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Oudergemlaan 214" ;
    ns1:parking-spaces "41" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/cccc8903-cbd3-4636-b0ea-992c16c004d3> a ns2:Building ;
    ns1:building-code "********62" ;
    ns1:construction-year "2002" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Business Center Muthgasse" ;
    ns1:parking-spaces "228" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/cd9ae8c1-0380-4ecc-98a3-5510442e3b2c> a ns2:Building ;
    ns1:building-code "1000000133" ;
    ns1:construction-year "2004" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Stortingsgata 6" ;
    ns1:parking-spaces "22" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/cfa4612e-4f03-4e50-a56f-6111375b2ab5> a ns2:Building ;
    ns1:building-code "********40" ;
    ns1:construction-year "2011" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Metropolis Haus" ;
    ns1:parking-spaces "60" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/d087f4c1-344d-401b-aff2-62dd21d74c68> a ns2:Building ;
    ns1:building-code "********41" ;
    ns1:construction-year "2002" ;
    ns1:energy-efficiency-class "E" ;
    ns1:name "Friedrich Carré II" ;
    ns1:parking-spaces "96" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Retail" ;
    ns1:valid-from "44926" .

<https://example.com/d3954c67-c035-4b3e-96bc-29515c31b7f5> a ns2:Building ;
    ns1:building-code "********09" ;
    ns1:construction-year "1991" ;
    ns1:energy-efficiency-class "C" ;
    ns1:name "Sunyard" ;
    ns1:parking-spaces "287" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/d54154fb-bd6f-4e1a-b2ef-c314e8ec6d4c> a ns2:Building ;
    ns1:building-code "********47" ;
    ns1:construction-year "2018" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "HighriseOne" ;
    ns1:parking-spaces "195" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/d577dc63-6bf1-4c89-8d9b-f4ee302444d0> a ns2:Building ;
    ns1:building-code "********69" ;
    ns1:construction-year "1998" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Opéra-Victoire" ;
    ns1:parking-spaces "227" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/d5ccdae6-a1b9-4d9c-8657-00aa64c5d7a8> a ns2:Building ;
    ns1:building-code "1000000137" ;
    ns1:construction-year "2003" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "IBC" ;
    ns1:parking-spaces "278" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/d63aaff1-e5c0-4fd8-b44d-5816efff4582> a ns2:Building ;
    ns1:building-code "********50" ;
    ns1:construction-year "2009" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "Bornhold Haus" ;
    ns1:parking-spaces "33" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/d86edecc-2881-4d9e-9372-faa829b541a6> a ns2:Building ;
    ns1:building-code "********16" ;
    ns1:construction-year "2007" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "Prime Parc, Bauteil C2" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/da857556-b790-4f2c-b193-523c29501fe1> a ns2:Building ;
    ns1:building-code "********19" ;
    ns1:construction-year "1977" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "Novum" ;
    ns1:parking-spaces "24" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/de3e19d1-687f-4ffe-83f5-96363f23275d> a ns2:Building ;
    ns1:building-code "1000000136" ;
    ns1:construction-year "2009" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Grzybowska Park" ;
    ns1:parking-spaces "66" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/deeddaa1-108b-4c09-a75d-5d867249b53f> a ns2:Building ;
    ns1:building-code "********64" ;
    ns1:construction-year "1998" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "El Triangle" ;
    ns1:parking-spaces "293" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/dfa528aa-af9f-48bd-b0a6-dd7320157c8b> a ns2:Building ;
    ns1:building-code "********97" ;
    ns1:construction-year "2000" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "54 Bd Haussmann" ;
    ns1:parking-spaces "73" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/dfcf1ea1-69bb-416d-a393-1ca5cd58aa5a> a ns2:Building ;
    ns1:building-code "********95" ;
    ns1:construction-year "1993" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Alta Diagonal" ;
    ns1:parking-spaces "427" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/e20a7cde-b6df-4ec1-9c21-00cb619c66a6> a ns2:Building ;
    ns1:building-code "********89" ;
    ns1:construction-year "2005" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Vinoly" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/e2d82b02-76d0-4ceb-9df8-5c6293a333a6> a ns2:Building ;
    ns1:building-code "********43" ;
    ns1:construction-year "2003" ;
    ns1:energy-efficiency-class "B" ;
    ns1:name "Theresie" ;
    ns1:parking-spaces "491" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/e64f2098-e585-4b4d-a69f-383d7eb6d1cd> a ns2:Building ;
    ns1:building-code "********59" ;
    ns1:construction-year "2020" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Rijnstraat 192" ;
    ns1:parking-spaces "81" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/e9c0ca5b-02e8-4732-8c7d-86632baba8c5> a ns2:Building ;
    ns1:building-code "********65" ;
    ns1:construction-year "1956" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Hotel \"Le Méridien\"" ;
    ns1:parking-spaces "23" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Hotel" ;
    ns1:valid-from "44926" .

<https://example.com/ea27f06d-b7a6-43e5-aee0-8ef57cb35279> a ns2:Building ;
    ns1:building-code "1000000115" ;
    ns1:construction-year "2005" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Emporium" ;
    ns1:parking-spaces "876" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/ec3dc6d1-f6ef-4099-909f-ab897b7583ac> a ns2:Building ;
    ns1:building-code "1000000131" ;
    ns1:construction-year "2017" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "One Rathbone Square" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/eed9967e-f5b7-4bca-a464-a0638dc60f09> a ns2:Building ;
    ns1:building-code "********57" ;
    ns1:construction-year "2019" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Venlo, Broekman" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/f4c3b7c3-eb91-44f3-bce1-ec2903ea58df> a ns2:Building ;
    ns1:building-code "1000000110" ;
    ns1:construction-year "2011" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Via Dogana Po 2a" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/f52ac78d-3bb0-4c2a-9777-39f65ccf1716> a ns2:Building ;
    ns1:building-code "********75" ;
    ns1:construction-year "1900" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Utoquai 29" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/f77edc3d-5b2c-42bb-b7e9-8efb5c218cb2> a ns2:Building ;
    ns1:building-code "********72" ;
    ns1:construction-year "2007" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "35 Rue de la Gare" ;
    ns1:parking-spaces "412" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/f978168a-f8c2-44de-a827-ffead32b8cba> a ns2:Building ;
    ns1:building-code "********52" ;
    ns1:construction-year "1998" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "De Resident" ;
    ns1:parking-spaces "233" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/fb1f443f-4478-4044-a8e7-d551d75802d1> a ns2:Building ;
    ns1:building-code "1000000102" ;
    ns1:construction-year "1800" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "33 Rue La Fayette" ;
    ns1:parking-spaces "301" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/fdb38a15-d380-49ae-8639-cc5861edc1a3> a ns2:Building ;
    ns1:building-code "1000000104" ;
    ns1:construction-year "2000" ;
    ns1:energy-efficiency-class "k" ;
    ns1:name "Palazzo Aporti" ;
    ns1:parking-spaces "241" ;
    ns1:primary-heating-type "Natural gas" ;
    ns1:primary-type-of-building "Office" ;
    ns1:valid-from "44926" .

<https://example.com/fe7d490e-2a8c-4329-8c6c-e413e0969bc4> a ns2:Building ;
    ns1:building-code "********33" ;
    ns1:construction-year "1999" ;
    ns1:energy-efficiency-class "D" ;
    ns1:name "FLZ – Fracht- und Logistik Zentrum" ;
    ns1:parking-spaces "0" ;
    ns1:primary-heating-type "District heating" ;
    ns1:primary-type-of-building "Industrial, Distribution Warehouse" ;
    ns1:valid-from "44926" .

