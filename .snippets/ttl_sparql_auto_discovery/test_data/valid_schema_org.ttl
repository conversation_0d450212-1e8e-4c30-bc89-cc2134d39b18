@prefix schema: <https://schema.org/> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

<http://example.org/restaurants/pizza-palace> a schema:Restaurant ;
    schema:name "Pizza Palace" ;
    schema:description "Authentic Italian pizza in downtown" ;
    schema:address [
        a schema:PostalAddress ;
        schema:streetAddress "123 Main Street" ;
        schema:addressLocality "Downtown" ;
        schema:addressRegion "NY" ;
        schema:postalCode "10001" ;
        schema:addressCountry "US"
    ] ;
    schema:telephone "******-PIZZA" ;
    schema:url <http://pizzapalace.example.org> ;
    schema:priceRange "$15-30" ;
    schema:servesCuisine "Italian" ;
    schema:aggregateRating [
        a schema:AggregateRating ;
        schema:ratingValue "4.5"^^xsd:decimal ;
        schema:reviewCount "127"^^xsd:integer ;
        schema:bestRating "5"^^xsd:integer ;
        schema:worstRating "1"^^xsd:integer
    ] .

<http://example.org/events/concert2024> a schema:MusicEvent ;
    schema:name "Summer Jazz Festival 2024" ;
    schema:description "Annual outdoor jazz festival featuring local and international artists" ;
    schema:startDate "2024-08-15T18:00:00"^^xsd:dateTime ;
    schema:endDate "2024-08-17T23:00:00"^^xsd:dateTime ;
    schema:location [
        a schema:Place ;
        schema:name "Central Park Amphitheater" ;
        schema:address [
            a schema:PostalAddress ;
            schema:addressLocality "Central City" ;
            schema:addressRegion "CA"
        ]
    ] ;
    schema:performer <http://example.org/artists/john-doe-quartet> ;
    schema:offers [
        a schema:Offer ;
        schema:price "45.00"^^xsd:decimal ;
        schema:priceCurrency "USD" ;
        schema:availability schema:InStock
    ] .

<http://example.org/products/laptop-x1> a schema:Product ;
    schema:name "UltraBook X1" ;
    schema:description "High-performance laptop for professionals" ;
    schema:brand [
        a schema:Brand ;
        schema:name "TechCorp"
    ] ;
    schema:model "X1-2024" ;
    schema:offers [
        a schema:Offer ;
        schema:price "1299.99"^^xsd:decimal ;
        schema:priceCurrency "USD" ;
        schema:availability schema:InStock ;
        schema:seller [
            a schema:Organization ;
            schema:name "Electronics Store"
        ]
    ] .
