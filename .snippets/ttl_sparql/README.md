# TTL SPARQL Querying Tools

This directory contains clean, production-ready Python tools for querying TTL (Turtle) RDF files using SPARQL. The tools are generic and work with any TTL file structure, featuring proper Python exception handling for robust error management.

## Scripts

### 1. `validate_sparql_query.py`

A standalone SPARQL query validation tool with comprehensive error reporting.

**Usage:**

```bash
# Validate a query string
python validate_sparql_query.py "SELECT ?s ?p ?o WHERE { ?s ?p ?o . }"

# Validate a query from file
python validate_sparql_query.py --file query.sparql

# Quiet mode (just returns valid/invalid)
python validate_sparql_query.py --quiet "ASK { ?s ?p ?o }"
```

### 2. `query_ttl_with_sparql.py`

A complete TTL querying engine with CLI support and robust exception handling.

**Usage:**

```bash
# Basic query
python query_ttl_with_sparql.py data.ttl "SELECT ?s ?p ?o WHERE { ?s ?p ?o . } LIMIT 10"

# Read query from file
python query_ttl_with_sparql.py data.ttl --query-file query.sparql

# JSON output
python query_ttl_with_sparql.py data.ttl "ASK { ?s ?p ?o }" --output json

# Skip validation for faster execution
python query_ttl_with_sparql.py data.ttl "SELECT * WHERE { ?s ?p ?o }" --no-validate

# Limit results displayed
python query_ttl_with_sparql.py data.ttl "SELECT * WHERE { ?s ?p ?o }" --limit-results 50
```

## Features

- **Generic Design**: Works with any TTL file structure or vocabulary
- **SPARQL 1.1 Support**: All query types (SELECT, ASK, CONSTRUCT, DESCRIBE)
- **Proper Exception Handling**: Custom exception hierarchy with detailed error context
- **CLI Interface**: Command-line argument support for TTL file input
- **Multiple Output Formats**: Text and JSON output options
- **File Input**: Support for reading queries from files
- **Performance Options**: Optional query validation for faster execution

## Dependencies

- Python 3.8+
- rdflib >= 7.0.0

Install dependencies:

```bash
pip install -r requirements.txt
```

## Exception Hierarchy

The tools use proper Python exceptions for error handling:

- **`QueryInputError`**: Invalid query input (empty, None, wrong type)
- **`SPARQLValidationError`**: SPARQL syntax validation errors with line/column info
- **`TTLFileError`**: TTL file loading errors (not found, parsing errors, permissions)
- **`SPARQLExecutionError`**: Query execution failures with graph context
- **`TTLEngineError`**: Engine state errors (no TTL loaded, etc.)

## Functions for Programmatic Use

### validate_sparql_query(query: str) -> str

Validates SPARQL query syntax and returns query type.

**Parameters:**

- `query` (str): The SPARQL query string to validate

**Returns:**

- `str`: Query type ('SELECT', 'ASK', 'CONSTRUCT', 'DESCRIBE', 'UNKNOWN')

**Raises:**

- `QueryInputError`: If query is empty, None, or not a string
- `SPARQLValidationError`: If query syntax is invalid (includes line/column info)

**Example:**

```python
from validate_sparql_query import validate_sparql_query

try:
    query_type = validate_sparql_query("SELECT ?s ?p ?o WHERE { ?s ?p ?o . }")
    print(f"Valid {query_type} query")
except SPARQLValidationError as e:
    print(f"Query validation failed: {e}")
```

### query_ttl_file(ttl_file_path: str, sparql_query: str, validate_first: bool = True) -> Dict[str, Any]

Loads a TTL file and executes a SPARQL query against it.

**Parameters:**

- `ttl_file_path` (str): Path to the TTL file
- `sparql_query` (str): SPARQL query to execute
- `validate_first` (bool): Whether to validate query first (default: True)

**Returns:**

- `Dict[str, Any]`: Result dictionary with 'results', 'query_type', 'result_count'

**Raises:**

- `TTLFileError`: TTL file loading issues
- `QueryInputError`: Invalid query input
- `SPARQLValidationError`: Query validation failures
- `SPARQLExecutionError`: Query execution failures

**Example:**

```python
from query_ttl_with_sparql import query_ttl_file, TTLFileError, SPARQLExecutionError

try:
    result = query_ttl_file("data.ttl", "SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10")
    print(f"Found {result['result_count']} results")
    for row in result['results']:
        print(row)
except TTLFileError as e:
    print(f"TTL file error: {e}")
except SPARQLExecutionError as e:
    print(f"Query execution error: {e}")
```

### TTLQueryEngine Class

For advanced usage, use the `TTLQueryEngine` class directly:

```python
from query_ttl_with_sparql import TTLQueryEngine, TTLFileError, SPARQLExecutionError

try:
    engine = TTLQueryEngine()
    engine.load_ttl_file("data.ttl")
    result = engine.execute_sparql_query("SELECT ?s ?p ?o WHERE { ?s ?p ?o } LIMIT 10")
    print(f"Query executed successfully: {result['result_count']} results")
except TTLFileError as e:
    print(f"Failed to load TTL file: {e}")
except SPARQLExecutionError as e:
    print(f"Query execution failed: {e}")
```

## Error Context

All exceptions provide rich context information:

- **File paths**: Absolute paths for better debugging
- **Line/column numbers**: For parsing errors
- **Graph statistics**: Number of triples and namespaces
- **Query preview**: Truncated query text for context
- **Exception type**: Original exception type for debugging
- **Detailed messages**: Human-readable error descriptions

## CLI Error Handling

Command-line interfaces handle exceptions gracefully:

- **Text mode**: Human-readable error messages to stderr
- **JSON mode**: Structured error information in JSON format
- **Exit codes**: 0 for success, 1 for any error
- **Detailed context**: File paths, query previews, and error types

## Examples

See `test_query.sparql` for an example SPARQL query file that demonstrates grouping and counting.

## Files

- `validate_sparql_query.py` - SPARQL validation tool with custom exceptions
- `query_ttl_with_sparql.py` - Complete TTL querying engine with exception hierarchy
- `requirements.txt` - Python dependencies
- `test_query.sparql` - Example SPARQL query file
- `assets/example.ttl` - Sample TTL file for testing
