#!/usr/bin/env python3
"""
Example of using the refactored TTL querying tools with proper exception handling.
This demonstrates the clean, Pythonic API using exceptions instead of error dictionaries.
"""

from query_ttl_with_sparql import TTLQueryEngine, query_ttl_file
from query_ttl_with_sparql import TTLFileError, SPARQLExecutionError, TTLEngineError
from validate_sparql_query import validate_sparql_query, SPARQLValidationError, QueryInputError

def main():
    print("=== Example: Clean Exception-Based API ===\n")
    
    # Example 1: Validation with proper exception handling
    print("1. SPARQL Query Validation:")
    try:
        query_type = validate_sparql_query("SELECT ?s ?p ?o WHERE { ?s ?p ?o . } LIMIT 5")
        print(f"   ✅ Valid {query_type} query")
    except SPARQLValidationError as e:
        print(f"   ❌ Invalid query: {e}")
    except QueryInputError as e:
        print(f"   ❌ Input error: {e}")
    
    # Example 2: TTL loading and querying
    print("\n2. TTL File Loading and Querying:")
    try:
        engine = TTLQueryEngine()
        engine.load_ttl_file("assets/example.ttl")
        print(f"   ✅ Loaded TTL file: {len(engine.graph)} triples, {len(engine.namespaces)} namespaces")
        
        result = engine.execute_sparql_query("SELECT (COUNT(*) AS ?count) WHERE { ?s ?p ?o }")
        print(f"   ✅ Query result: {result['results'][0]}")
        
    except TTLFileError as e:
        print(f"   ❌ TTL file error: {e}")
    except SPARQLExecutionError as e:
        print(f"   ❌ Query execution error: {e}")
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
    
    # Example 3: Convenience function
    print("\n3. One-Step Query Function:")
    try:
        result = query_ttl_file("assets/example.ttl", "ASK { ?s ?p ?o }")
        print(f"   ✅ ASK query result: {result['results']}")
    except (TTLFileError, SPARQLExecutionError, SPARQLValidationError) as e:
        print(f"   ❌ Error: {e}")
    
    # Example 4: Error demonstration
    print("\n4. Error Handling Demonstration:")
    try:
        result = query_ttl_file("nonexistent.ttl", "SELECT ?s ?p ?o WHERE { ?s ?p ?o }")
        print(f"   This shouldn't print: {result}")
    except TTLFileError as e:
        print(f"   ❌ Expected error caught: {type(e).__name__}")
        print(f"   📝 Error details: {str(e)[:100]}...")
    
    print("\n=== Clean API with proper Python exceptions! ===")

if __name__ == "__main__":
    main()
