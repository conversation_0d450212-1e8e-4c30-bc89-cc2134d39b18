[{"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:bf0bf34a-a3a2-4616-a0f7-811a11641cc2 ibpdi:hasBuilding inst:57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "bf0bf34a-a3a2-4616-a0f7-811a11641cc2", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Barthstraße", "HouseNumber": "12", "PostalCode": "80339", "City": "München", "Country": "Germany"}}, {"id": "57cf1f63-0b7f-4c60-bf81-4bd93a0d4ab2", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000001", "Name": "RONDO", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "324", "EnergyEfficiencyClass": "D", "ConstructionYear": "2001"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:1b0cf251-2bb1-4a54-a669-3dd89d3d1412 ibpdi:hasBuilding inst:10d7c8c3-bc2f-4166-97d7-d11f04a39d82.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "1b0cf251-2bb1-4a54-a669-3dd89d3d1412", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Friedrichstraße", "HouseNumber": "50-55", "PostalCode": "10117", "City": "Berlin", "Country": "Germany"}}, {"id": "10d7c8c3-bc2f-4166-97d7-d11f04a39d82", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000002", "Name": "Checkpoint Charlie", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "207", "EnergyEfficiencyClass": "C", "ConstructionYear": "1999"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:b57b570f-8710-434f-b2f3-eaa809241db0 ibpdi:hasBuilding inst:be0f3b3e-c7ed-4178-8cc4-6114ad605936.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "b57b570f-8710-434f-b2f3-eaa809241db0", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Reinhardtstraße", "HouseNumber": "52", "PostalCode": "10117", "City": "Berlin", "Country": "Germany"}}, {"id": "be0f3b3e-c7ed-4178-8cc4-6114ad605936", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000003", "Name": "Waterfalls Berlin", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "71", "EnergyEfficiencyClass": "C", "ConstructionYear": "2003"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7fe74459-5abc-42f4-99c4-d51f6eb05c97 ibpdi:hasBuilding inst:1f1024af-c0f0-44f6-881b-77dae01bbd6a.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7fe74459-5abc-42f4-99c4-d51f6eb05c97", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Cargo City Sud Gebiude", "HouseNumber": "571", "PostalCode": "60549", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "1f1024af-c0f0-44f6-881b-77dae01bbd6a", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Industrial, Distribution Warehouse", "BuildingCode": "1000000004", "Name": "AIR CARGO Center", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "D", "ConstructionYear": "2003"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:018ac746-2503-4f2e-b681-8cb37d2f0e29 ibpdi:hasBuilding inst:1892ebb9-9532-4dee-bddc-a7044da700a2.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "018ac746-2503-4f2e-b681-8cb37d2f0e29", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Mainzer Landstraße", "HouseNumber": "172-190", "PostalCode": "60327", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "1892ebb9-9532-4dee-bddc-a7044da700a2", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000005", "Name": "Atrium Plaza", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "151", "EnergyEfficiencyClass": "E", "ConstructionYear": "2003"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7c1f5556-2aa4-420f-a504-dd1b2ed9984a ibpdi:hasBuilding inst:c7848807-75bd-4dc2-8bec-9df8c7949fe2.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7c1f5556-2aa4-420f-a504-dd1b2ed9984a", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Karolinenstraße", "HouseNumber": "32-36", "PostalCode": "90402", "City": "Nürnberg", "Country": "Germany"}}, {"id": "c7848807-75bd-4dc2-8bec-9df8c7949fe2", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000006", "Name": "<PERSON><PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "107", "EnergyEfficiencyClass": "D", "ConstructionYear": "2003"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:1830a091-367f-434a-b2eb-4e495c16d3ff ibpdi:hasBuilding inst:57cd12c2-f87c-48cf-be65-66574c335774.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "1830a091-367f-434a-b2eb-4e495c16d3ff", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Thouretstrae", "HouseNumber": "2", "PostalCode": "70173", "City": "Stuttgart", "Country": "Germany"}}, {"id": "57cd12c2-f87c-48cf-be65-66574c335774", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000007", "Name": "s´Zentrum", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "87", "EnergyEfficiencyClass": "B", "ConstructionYear": "2002"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:d036555b-4752-44a8-bad4-b4bdf9c3c6b5 ibpdi:hasBuilding inst:b663e664-9bcb-4adb-afb0-b3f7118ecec5.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "d036555b-4752-44a8-bad4-b4bdf9c3c6b5", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Straße des Juni", "HouseNumber": "106-108", "PostalCode": "10623", "City": "Berlin", "Country": "Germany"}}, {"id": "b663e664-9bcb-4adb-afb0-b3f7118ecec5", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000008", "Name": "Tiergarten Tower", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "157", "EnergyEfficiencyClass": "D", "ConstructionYear": "2005"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:638051f6-99d2-4784-8907-c9488441ab6a ibpdi:hasBuilding inst:d3954c67-c035-4b3e-96bc-29515c31b7f5.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "638051f6-99d2-4784-8907-c9488441ab6a", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "St.<PERSON> Straßeaße", "HouseNumber": "58-68", "PostalCode": "81541", "City": "München", "Country": "Germany"}}, {"id": "d3954c67-c035-4b3e-96bc-29515c31b7f5", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000009", "Name": "Sunyard", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "287", "EnergyEfficiencyClass": "C", "ConstructionYear": "1991"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7ff07085-d6c9-4e05-8a75-9f609aa16c58 ibpdi:hasBuilding inst:65c6d94e-ba3f-4c7e-8c49-942e1712cf1e.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7ff07085-d6c9-4e05-8a75-9f609aa16c58", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON>", "HouseNumber": "18-20", "PostalCode": "40213", "City": "Düsseldorf", "Country": "Germany"}}, {"id": "65c6d94e-ba3f-4c7e-8c49-942e1712cf1e", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000010", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "414", "EnergyEfficiencyClass": "B", "ConstructionYear": "2003"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7dfc816c-cce4-4dd7-9a89-c2c0a04731f1 ibpdi:hasBuilding inst:ad8f9f50-c667-45e6-ac48-ac8d7ed7b29b.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7dfc816c-cce4-4dd7-9a89-c2c0a04731f1", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Anna-<PERSON><PERSON><PERSON><PERSON>", "HouseNumber": "2", "PostalCode": "10178", "City": "Berlin", "Country": "Germany"}}, {"id": "ad8f9f50-c667-45e6-ac48-ac8d7ed7b29b", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000011", "Name": "SpreePalais am Dom", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "166", "EnergyEfficiencyClass": "D", "ConstructionYear": "2002"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:01bea4e6-fee0-40dc-a829-902d901d0a67 ibpdi:hasBuilding inst:8995106d-e483-4266-a3c7-65120a6f3bbe.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "01bea4e6-fee0-40dc-a829-902d901d0a67", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HouseNumber": "1-3", "PostalCode": "20355", "City": "Hamburg", "Country": "Germany"}}, {"id": "8995106d-e483-4266-a3c7-65120a6f3bbe", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000012", "Name": "Fleethof", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "236", "EnergyEfficiencyClass": "E", "ConstructionYear": "1993"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:30d380a3-1509-4e99-af47-01846458182b ibpdi:hasBuilding inst:6ac5af7c-329c-47b1-82c0-e21c10454b38.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "30d380a3-1509-4e99-af47-01846458182b", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Stockstraße", "HouseNumber": "1", "PostalCode": "65479", "City": "Ra<PERSON><PERSON>", "Country": "Germany"}}, {"id": "6ac5af7c-329c-47b1-82c0-e21c10454b38", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000013", "Name": "Prime Parc, Bauteil A1-A5", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "C", "ConstructionYear": "2000"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:e2ed1add-caca-4511-95d1-5b928f7c3059 ibpdi:hasBuilding inst:89afdf02-d465-4814-85ec-9ac00ef4c580.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "e2ed1add-caca-4511-95d1-5b928f7c3059", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Stockstrage", "HouseNumber": "1", "PostalCode": "65479", "City": "Ra<PERSON><PERSON>", "Country": "Germany"}}, {"id": "89afdf02-d465-4814-85ec-9ac00ef4c580", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000014", "Name": "<PERSON> <PERSON>, Bauteil B1-B8", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "270", "EnergyEfficiencyClass": "D", "ConstructionYear": "2001"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:2e1072c6-e9fe-4f24-9536-248872883738 ibpdi:hasBuilding inst:c592f971-2fda-4169-8e89-c7f296a3300f.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "2e1072c6-e9fe-4f24-9536-248872883738", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Kelsterbacher Str.", "HouseNumber": "2", "PostalCode": "65479", "City": "Ra<PERSON><PERSON>", "Country": "Germany"}}, {"id": "c592f971-2fda-4169-8e89-c7f296a3300f", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000015", "Name": "Prime Parc, Bauteil C1 + Parkhaus", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "283", "EnergyEfficiencyClass": "B", "ConstructionYear": "2004"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:76b5860b-ad34-44ca-909f-15d905d9e8fc ibpdi:hasBuilding inst:d86edecc-2881-4d9e-9372-faa829b541a6.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "76b5860b-ad34-44ca-909f-15d905d9e8fc", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Am Prime-Pare", "HouseNumber": "2", "PostalCode": "65479", "City": "Ra<PERSON><PERSON>", "Country": "Germany"}}, {"id": "d86edecc-2881-4d9e-9372-faa829b541a6", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000016", "Name": "Prime Parc, Bauteil C2", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "D", "ConstructionYear": "2007"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:c58327a2-b70f-4808-9be6-f3f61c45a367 ibpdi:hasBuilding inst:075bf17a-11cc-4609-83ed-5b172ae187f2.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "c58327a2-b70f-4808-9be6-f3f61c45a367", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Magnusstrafe", "HouseNumber": "11", "PostalCode": "50672", "City": "Köln", "Country": "Germany"}}, {"id": "075bf17a-11cc-4609-83ed-5b172ae187f2", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000017", "Name": "Magnusstraße 11", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "149", "EnergyEfficiencyClass": "A", "ConstructionYear": "1999"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7696f6fa-4117-4c41-aa92-3b1a9b52d852 ibpdi:hasBuilding inst:b51655df-18bc-4b4f-b8a6-48337c68757d.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7696f6fa-4117-4c41-aa92-3b1a9b52d852", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON>auer Landstrae", "HouseNumber": "126-128", "PostalCode": "60314", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "b51655df-18bc-4b4f-b8a6-48337c68757d", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000018", "Name": "Lighttower", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "57", "EnergyEfficiencyClass": "C", "ConstructionYear": "1966"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:ef0b2a11-9e96-4426-bf4d-8b069bc5db54 ibpdi:hasBuilding inst:da857556-b790-4f2c-b193-523c29501fe1.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "ef0b2a11-9e96-4426-bf4d-8b069bc5db54", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Grofe Gallusstraße", "HouseNumber": "9", "PostalCode": "60311", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "da857556-b790-4f2c-b193-523c29501fe1", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000019", "Name": "Novum", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "24", "EnergyEfficiencyClass": "B", "ConstructionYear": "1977"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:34fb1c83-74f2-4bc6-a77a-fabee29812f9 ibpdi:hasBuilding inst:4f72a0ac-ef2f-4e33-aa1b-c34572988b6a.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "34fb1c83-74f2-4bc6-a77a-fabee29812f9", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Frankfurter Straßeaße", "HouseNumber": "168-176", "PostalCode": "63263", "City": "Neu-Isenburg", "Country": "Germany"}}, {"id": "4f72a0ac-ef2f-4e33-aa1b-c34572988b6a", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000020", "Name": "Isenburg-Zentrum", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "1530", "EnergyEfficiencyClass": "C", "ConstructionYear": "1972"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:2ac8ad0b-5f80-4d95-bedc-199479bb3931 ibpdi:hasBuilding inst:06ccb9e5-e34b-4637-aaa4-d5e345cc7d22.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "2ac8ad0b-5f80-4d95-bedc-199479bb3931", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "HouseNumber": "2", "PostalCode": "60486", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "06ccb9e5-e34b-4637-aaa4-d5e345cc7d22", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000021", "Name": "Poseidon", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "432", "EnergyEfficiencyClass": "E", "ConstructionYear": "1986"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:06c10c44-8944-4842-8538-20d5aba3f7bb ibpdi:hasBuilding inst:a2e0b5f8-b290-440f-bdfb-4861730dc59a.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "06c10c44-8944-4842-8538-20d5aba3f7bb", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Ulmenstraße", "HouseNumber": "30", "PostalCode": "60325", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "a2e0b5f8-b290-440f-bdfb-4861730dc59a", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000022", "Name": "Westend Sky", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "122", "EnergyEfficiencyClass": "C", "ConstructionYear": "1975"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:60f54207-6c9f-4e75-bf57-6fc7a0cc9f91 ibpdi:hasBuilding inst:5045a210-29bb-4b4e-b073-3026d291047d.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "60f54207-6c9f-4e75-bf57-6fc7a0cc9f91", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Mainzer Landstratle", "HouseNumber": "293", "PostalCode": "60326", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "5045a210-29bb-4b4e-b073-3026d291047d", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000023", "Name": "Mainzer Landstraße 293", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "342", "EnergyEfficiencyClass": "C", "ConstructionYear": "1992"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:52c8308e-0275-4c03-ac5f-32fd2820bd92 ibpdi:hasBuilding inst:cc4e3285-f6a0-4e9b-a204-4a70b555ab25.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "52c8308e-0275-4c03-ac5f-32fd2820bd92", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON>", "HouseNumber": "4", "PostalCode": "80331", "City": "München", "Country": "Germany"}}, {"id": "cc4e3285-f6a0-4e9b-a204-4a70b555ab25", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000024", "Name": "LEOMAX", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "32", "EnergyEfficiencyClass": "D", "ConstructionYear": "1922"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:c00a2afa-7750-402f-a2b0-14fdbf69e046 ibpdi:hasBuilding inst:a4288287-712a-4c30-86f5-26f4c9b39200.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "c00a2afa-7750-402f-a2b0-14fdbf69e046", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Bayerstraße", "HouseNumber": "21/V", "PostalCode": "80335", "City": "München", "Country": "Germany"}}, {"id": "a4288287-712a-4c30-86f5-26f4c9b39200", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000025", "Name": "Bayerstraße 21/V", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "24", "EnergyEfficiencyClass": "A", "ConstructionYear": "1975"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:8036cf7f-0f2e-4e72-9585-2a0213cc4433 ibpdi:hasBuilding inst:11f9f028-6ee1-495b-9978-3e1a23d7c3fb.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "8036cf7f-0f2e-4e72-9585-2a0213cc4433", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "KonigstraBe", "HouseNumber": "14", "PostalCode": "70173", "City": "Stuttgart", "Country": "Germany"}}, {"id": "11f9f028-6ee1-495b-9978-3e1a23d7c3fb", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000026", "Name": "Königstraße 14", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "13", "EnergyEfficiencyClass": "B", "ConstructionYear": "1925"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:534b5c05-f162-4d18-bc0e-a68ae9ac21e2 ibpdi:hasBuilding inst:899d7524-f048-4006-aeb8-7fc0410c05b4.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "534b5c05-f162-4d18-bc0e-a68ae9ac21e2", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Herriotstrafe", "HouseNumber": "3", "PostalCode": "60528", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "899d7524-f048-4006-aeb8-7fc0410c05b4", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000027", "Name": "<PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "673", "EnergyEfficiencyClass": "D", "ConstructionYear": "2003"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:b92396d2-7746-4991-a577-1305fc5a2eb1 ibpdi:hasBuilding inst:10bd9e8d-dc9f-4cd9-a131-045a9c596498.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "b92396d2-7746-4991-a577-1305fc5a2eb1", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Landaubogen", "HouseNumber": "10", "PostalCode": "81373", "City": "München", "Country": "Germany"}}, {"id": "10bd9e8d-dc9f-4cd9-a131-045a9c596498", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000028", "Name": "Alpha-Haus", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "291", "EnergyEfficiencyClass": "D", "ConstructionYear": "1990"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:73a9aab9-5599-4056-b3cd-40ab314eb13b ibpdi:hasBuilding inst:a01e6e44-48b0-41c4-aa7f-2fa3298e0f4c.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "73a9aab9-5599-4056-b3cd-40ab314eb13b", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "An den Dominikanern", "HouseNumber": "6-8", "PostalCode": "50668", "City": "Köln", "Country": "Germany"}}, {"id": "a01e6e44-48b0-41c4-aa7f-2fa3298e0f4c", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000029", "Name": "Lindner Hotel", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "F", "ConstructionYear": "2000"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:04baa1d3-3007-41e6-ba27-1b124e96706c ibpdi:hasBuilding inst:986ee284-f90a-4d68-a0a1-7294fe92de88.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "04baa1d3-3007-41e6-ba27-1b124e96706c", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON>", "HouseNumber": "22", "PostalCode": "41468", "City": "<PERSON><PERSON><PERSON>", "Country": "Germany"}}, {"id": "986ee284-f90a-4d68-a0a1-7294fe92de88", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Industrial, Distribution Warehouse", "BuildingCode": "1000000030", "Name": "Logistikzentrum Rhein-Ruhr", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "C", "ConstructionYear": "2009"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:ff24a9bc-6ab0-40cd-9c66-82a624ef3ea6 ibpdi:hasBuilding inst:a3251601-d4a3-4345-ba97-c7cf47062948.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "ff24a9bc-6ab0-40cd-9c66-82a624ef3ea6", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Torstraße", "HouseNumber": "49", "PostalCode": "10119", "City": "Berlin", "Country": "Germany"}}, {"id": "a3251601-d4a3-4345-ba97-c7cf47062948", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000031", "Name": "Schönhauser Tor", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "124", "EnergyEfficiencyClass": "F", "ConstructionYear": "1996"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:aeb649e7-a519-44d8-939f-e7210a2c5cba ibpdi:hasBuilding inst:8c5f61ab-40f1-4aac-9d42-f5c3abff7b6f.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "aeb649e7-a519-44d8-939f-e7210a2c5cba", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Cargo City Sud Gebaude", "HouseNumber": "558", "PostalCode": "60549", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "8c5f61ab-40f1-4aac-9d42-f5c3abff7b6f", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Industrial, Distribution Warehouse", "BuildingCode": "1000000032", "Name": "FLZ – Fracht- und Logistik Zentrum", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "E", "ConstructionYear": "2000"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:955aafb7-4870-42bd-b447-1fe6ced2e879 ibpdi:hasBuilding inst:fe7d490e-2a8c-4329-8c6c-e413e0969bc4.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "955aafb7-4870-42bd-b447-1fe6ced2e879", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Cargo City Sud Gebaude", "HouseNumber": "558", "PostalCode": "60549", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "fe7d490e-2a8c-4329-8c6c-e413e0969bc4", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Industrial, Distribution Warehouse", "BuildingCode": "1000000033", "Name": "FLZ – Fracht- und Logistik Zentrum", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "D", "ConstructionYear": "1999"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:9d82f641-36f6-465c-9b5b-947abd3ed1d3 ibpdi:hasBuilding inst:0fd04dd8-da57-4c66-a5b8-ecb693f833f4.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "9d82f641-36f6-465c-9b5b-947abd3ed1d3", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "GroBe Elostraße", "HouseNumber": "14", "PostalCode": "22767", "City": "Hamburg", "Country": "Germany"}}, {"id": "0fd04dd8-da57-4c66-a5b8-ecb693f833f4", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000034", "Name": "Große Elbstraße 14", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "C", "ConstructionYear": "1966"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:565f7e3e-50b6-4fcc-b865-d7d53cf1515f ibpdi:hasBuilding inst:3c04623f-e668-40e8-81a8-1945d6e577d8.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "565f7e3e-50b6-4fcc-b865-d7d53cf1515f", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "HouseNumber": "31", "PostalCode": "28197", "City": "Bremen", "Country": "Germany"}}, {"id": "3c04623f-e668-40e8-81a8-1945d6e577d8", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Industrial, Distribution Warehouse", "BuildingCode": "1000000035", "Name": "Logistikzentrum Bremen", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "C", "ConstructionYear": "2009"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:0d2d08d8-62da-415d-83f3-d6796810c147 ibpdi:hasBuilding inst:42ee10cc-3dfa-496b-b165-8deeb57f63ea.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "0d2d08d8-62da-415d-83f3-d6796810c147", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Hobe Bleichen", "HouseNumber": "11", "PostalCode": "20354", "City": "Hamburg", "Country": "Germany"}}, {"id": "42ee10cc-3dfa-496b-b165-8deeb57f63ea", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000036", "Name": "Hohe Bleichen 11", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "34", "EnergyEfficiencyClass": "D", "ConstructionYear": "2008"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:fd3e3af3-a573-4a90-8910-1658cbf7be5d ibpdi:hasBuilding inst:763d5362-1d55-4319-8832-f931e89dd3d8.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "fd3e3af3-a573-4a90-8910-1658cbf7be5d", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON>stra<PERSON>", "HouseNumber": "$9", "PostalCode": "80636", "City": "München", "Country": "Germany"}}, {"id": "763d5362-1d55-4319-8832-f931e89dd3d8", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000037", "Name": "Arnulfstraße 59", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "210", "EnergyEfficiencyClass": "D", "ConstructionYear": "2010"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:3ac9d6af-380d-4db7-a3be-5b24400c1edb ibpdi:hasBuilding inst:99120dc1-8ee9-46c3-b465-c7e890895a76.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "3ac9d6af-380d-4db7-a3be-5b24400c1edb", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "GeorgenstraBe", "HouseNumber": "24", "PostalCode": "10117", "City": "Berlin", "Country": "Germany"}}, {"id": "99120dc1-8ee9-46c3-b465-c7e890895a76", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000038", "Name": "<PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "201", "EnergyEfficiencyClass": "E", "ConstructionYear": "2002"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:aa90da7f-a054-4440-987d-b54aedd519e5 ibpdi:hasBuilding inst:9bdb9a60-6beb-40da-82cd-d0f8b556ce9d.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "aa90da7f-a054-4440-987d-b54aedd519e5", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON><PERSON>", "HouseNumber": "1", "PostalCode": "38100", "City": "Braunschweig", "Country": "Germany"}}, {"id": "9bdb9a60-6beb-40da-82cd-d0f8b556ce9d", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000039", "Name": "Schloss-Arkaden", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "1271", "EnergyEfficiencyClass": "F", "ConstructionYear": "2007"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:b9cca905-ab1c-4958-b3ed-bc37857afa44 ibpdi:hasBuilding inst:cfa4612e-4f03-4e50-a56f-6111375b2ab5.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "b9cca905-ab1c-4958-b3ed-bc37857afa44", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Dammtorstrafe", "HouseNumber": "29-32", "PostalCode": "20354", "City": "Hamburg", "Country": "Germany"}}, {"id": "cfa4612e-4f03-4e50-a56f-6111375b2ab5", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000040", "Name": "Metropolis Haus", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "60", "EnergyEfficiencyClass": "C", "ConstructionYear": "2011"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:0d35fb07-63a9-4280-a4b2-cb808f0211e6 ibpdi:hasBuilding inst:d087f4c1-344d-401b-aff2-62dd21d74c68.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "0d35fb07-63a9-4280-a4b2-cb808f0211e6", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Dorotheenstraße", "HouseNumber": "54", "PostalCode": "10117", "City": "Berlin", "Country": "Germany"}}, {"id": "d087f4c1-344d-401b-aff2-62dd21d74c68", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000041", "Name": "<PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "96", "EnergyEfficiencyClass": "E", "ConstructionYear": "2002"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:fc4c1b2b-349d-4e65-a9dd-4e6ecbfb6037 ibpdi:hasBuilding inst:3942f7e3-440b-47b2-9c0a-587c5543c71e.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "fc4c1b2b-349d-4e65-a9dd-4e6ecbfb6037", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Neumarkt", "HouseNumber": "2-4", "PostalCode": "50667", "City": "Köln", "Country": "Germany"}}, {"id": "3942f7e3-440b-47b2-9c0a-587c5543c71e", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000042", "Name": "Neumarkt Galerie", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "141", "EnergyEfficiencyClass": "C", "ConstructionYear": "2013"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:a06ce1f9-d666-48fa-b693-b3beefd98ddd ibpdi:hasBuilding inst:e2d82b02-76d0-4ceb-9df8-5c6293a333a6.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "a06ce1f9-d666-48fa-b693-b3beefd98ddd", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HouseNumber": "77", "PostalCode": "80339", "City": "München", "Country": "Germany"}}, {"id": "e2d82b02-76d0-4ceb-9df8-5c6293a333a6", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000043", "Name": "<PERSON>sie", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "491", "EnergyEfficiencyClass": "B", "ConstructionYear": "2003"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:88d9f843-a782-41db-a5c6-e909410cc4d6 ibpdi:hasBuilding inst:5154cc9b-6a58-4bb4-a015-7bad385f7c0c.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "88d9f843-a782-41db-a5c6-e909410cc4d6", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Goethestrage", "HouseNumber": "2", "PostalCode": "80336", "City": "München", "Country": "Germany"}}, {"id": "5154cc9b-6a58-4bb4-a015-7bad385f7c0c", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000044", "Name": "Hotel Le Méridien", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "72", "EnergyEfficiencyClass": "C", "ConstructionYear": "2002"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:77f07fbc-e2c3-438e-b72a-ce4bb3972a5d ibpdi:hasBuilding inst:8c8e19d6-5c88-49f1-93a1-d16c78b942cf.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "77f07fbc-e2c3-438e-b72a-ce4bb3972a5d", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Bayerstrae", "HouseNumber": "12", "PostalCode": "80335", "City": "München", "Country": "Germany"}}, {"id": "8c8e19d6-5c88-49f1-93a1-d16c78b942cf", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000045", "Name": "Sofitel Munich Bayerpost", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "70", "EnergyEfficiencyClass": "F", "ConstructionYear": "2004"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:9b6472d3-93ae-4dd2-a2a8-2030afc8ed1a ibpdi:hasBuilding inst:2c5ac2b6-7887-4b38-9203-c94df0e68b67.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "9b6472d3-93ae-4dd2-a2a8-2030afc8ed1a", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Donaustraße", "HouseNumber": "2", "PostalCode": "65451", "City": "Ke<PERSON>terbach", "Country": "Germany"}}, {"id": "2c5ac2b6-7887-4b38-9203-c94df0e68b67", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000046", "Name": "Mönchhof Frankfurt Airport", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "A", "ConstructionYear": "2017"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:02a175f3-9042-44f7-bb2f-a3b9c5997bb8 ibpdi:hasBuilding inst:d54154fb-bd6f-4e1a-b2ef-c314e8ec6d4c.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "02a175f3-9042-44f7-bb2f-a3b9c5997bb8", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rosenheimer Straßeaße", "HouseNumber": "141)", "PostalCode": "81671", "City": "München", "Country": "Germany"}}, {"id": "d54154fb-bd6f-4e1a-b2ef-c314e8ec6d4c", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000047", "Name": "HighriseOne", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "195", "EnergyEfficiencyClass": "B", "ConstructionYear": "2018"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:2ce12f63-fcc1-4ef9-bd96-01fe849c6896 ibpdi:hasBuilding inst:4620ead9-e050-4d4c-9b7b-d8bff52936b8.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "2ce12f63-fcc1-4ef9-bd96-01fe849c6896", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Einkaufszentrum Das Schloss Schlostrae", "HouseNumber": "34", "PostalCode": "12163", "City": "Berlin", "Country": "Germany"}}, {"id": "4620ead9-e050-4d4c-9b7b-d8bff52936b8", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000048", "Name": "Das Schloss Shoppingcenter", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "580", "EnergyEfficiencyClass": "D", "ConstructionYear": "2006"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:dd002b44-8118-4469-8cc5-1db5bcf63c0f ibpdi:hasBuilding inst:858414b0-9520-4d68-afac-b856688eb3d7.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "dd002b44-8118-4469-8cc5-1db5bcf63c0f", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Spreestraße", "HouseNumber": "1", "PostalCode": "65451", "City": "Ke<PERSON>terbach", "Country": "Germany"}}, {"id": "858414b0-9520-4d68-afac-b856688eb3d7", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000049", "Name": "Logistikzentrum FFM-Airport, 2. BA", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "B", "ConstructionYear": "2020"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7d2c59cf-1cae-4f50-b137-ac79103499da ibpdi:hasBuilding inst:d63aaff1-e5c0-4fd8-b44d-5816efff4582.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7d2c59cf-1cae-4f50-b137-ac79103499da", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Never Wall", "HouseNumber": "84", "PostalCode": "20354", "City": "Hamburg", "Country": "Germany"}}, {"id": "d63aaff1-e5c0-4fd8-b44d-5816efff4582", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000050", "Name": "<PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "33", "EnergyEfficiencyClass": "B", "ConstructionYear": "2009"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:b441a9c9-c9d1-418d-a5e8-a1a1e8550235 ibpdi:hasBuilding inst:46423443-e41a-487d-a07a-c7a9fd5e1ff4.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "b441a9c9-c9d1-418d-a5e8-a1a1e8550235", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Alte FlughafenstraBe", "HouseNumber": "3504", "PostalCode": "40474", "City": "Düsseldorf", "Country": "Germany"}}, {"id": "46423443-e41a-487d-a07a-c7a9fd5e1ff4", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000051", "Name": "CUBES", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "218", "EnergyEfficiencyClass": "B", "ConstructionYear": "2012"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7893c76e-eefd-4bd4-8ada-e7982d37508f ibpdi:hasBuilding inst:f978168a-f8c2-44de-a827-ffead32b8cba.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7893c76e-eefd-4bd4-8ada-e7982d37508f", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "armassuspleinS", "PostalCode": "2515", "City": "XP Den Haag", "Country": "Netherlands"}}, {"id": "f978168a-f8c2-44de-a827-ffead32b8cba", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000052", "Name": "De Resident", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "233", "EnergyEfficiencyClass": "k", "ConstructionYear": "1998"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:65e65ff0-7dcd-4fdb-b4be-b411917bf644 ibpdi:hasBuilding inst:52ca00a9-2cbe-4f54-8ea8-7b9f62c29f5d.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "65e65ff0-7dcd-4fdb-b4be-b411917bf644", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Nobelstraat", "HouseNumber": "10", "PostalCode": "5807", "City": "GA Oirlo", "Country": "Netherlands"}}, {"id": "52ca00a9-2cbe-4f54-8ea8-7b9f62c29f5d", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000053", "Name": "Distribution Center Flextronics", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2009"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:dd56c51f-8d9b-4a1a-bffc-9c3386c2d868 ibpdi:hasBuilding inst:b4d50434-46d9-4bd1-b7fd-c9336e157401.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "dd56c51f-8d9b-4a1a-bffc-9c3386c2d868", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON><PERSON>", "HouseNumber": "595", "PostalCode": "1017", "City": "CE Amsterdam", "Country": "Netherlands"}}, {"id": "b4d50434-46d9-4bd1-b7fd-c9336e157401", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "**********", "Name": "The Bank", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "110", "EnergyEfficiencyClass": "k", "ConstructionYear": "1931"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:bac81893-ffc3-44cb-9de1-de34ff78cb73 ibpdi:hasBuilding inst:a96f509a-1ce8-4a30-9e24-0144bd202cf2.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "bac81893-ffc3-44cb-9de1-de34ff78cb73", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON>", "HouseNumber": "59-72", "PostalCode": "1012", "City": "AD Amsterdam", "Country": "Netherlands"}}, {"id": "a96f509a-1ce8-4a30-9e24-0144bd202cf2", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000055", "Name": "Hotel NH Collection Amsterdam", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "26", "EnergyEfficiencyClass": "k", "ConstructionYear": "2016"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:31d6ca19-8a41-4167-8cc0-932923518399 ibpdi:hasBuilding inst:8e69a400-3087-4b3d-93a4-fcf0ba1dafd3.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "31d6ca19-8a41-4167-8cc0-932923518399", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Coolsingel", "HouseNumber": "63", "PostalCode": "3012", "City": "AB Rotterdam", "Country": "Netherlands"}}, {"id": "8e69a400-3087-4b3d-93a4-fcf0ba1dafd3", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000056", "Name": "Cool 63", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "37", "EnergyEfficiencyClass": "k", "ConstructionYear": "1954"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:1cbd2116-7b88-4f4b-9c35-c6e947928c70 ibpdi:hasBuilding inst:eed9967e-f5b7-4bca-a464-a0638dc60f09.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "1cbd2116-7b88-4f4b-9c35-c6e947928c70", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Doctor <PERSON>", "HouseNumber": "8", "PostalCode": "5928", "City": "NW Venlo", "Country": "Netherlands"}}, {"id": "eed9967e-f5b7-4bca-a464-a0638dc60f09", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000057", "Name": "<PERSON><PERSON><PERSON>, Broekman", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2019"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:d3b37ffb-44f7-4cd3-8da7-22fa34417eb8 ibpdi:hasBuilding inst:370d688f-fbac-4b18-9444-8ee31e05bb68.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "d3b37ffb-44f7-4cd3-8da7-22fa34417eb8", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Logistiekweg", "HouseNumber": "2", "PostalCode": "5928", "City": "SC Venlo", "Country": "Netherlands"}}, {"id": "370d688f-fbac-4b18-9444-8ee31e05bb68", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000058", "Name": "vidaXL Phase 3", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2020"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:009cb9c7-07d7-48fd-a539-672fbe6f2652 ibpdi:hasBuilding inst:e64f2098-e585-4b4d-a69f-383d7eb6d1cd.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "009cb9c7-07d7-48fd-a539-672fbe6f2652", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Aijstraat", "HouseNumber": "192", "PostalCode": "1079", "City": "HS Amsterdam", "Country": "Netherlands"}}, {"id": "e64f2098-e585-4b4d-a69f-383d7eb6d1cd", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000059", "Name": "Rijnstraat 192", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "81", "EnergyEfficiencyClass": "k", "ConstructionYear": "2020"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:ae3ccd79-1850-4ef2-b9ef-fab0b13bf95b ibpdi:hasBuilding inst:ccc75006-4473-44b9-8776-f4bd971be866.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "ae3ccd79-1850-4ef2-b9ef-fab0b13bf95b", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Ousergemlaan", "HouseNumber": "214", "PostalCode": "1040", "City": "Etterbeek", "Country": "Belgium"}}, {"id": "ccc75006-4473-44b9-8776-f4bd971be866", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000060", "Name": "Oudergemlaan 214", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "41", "EnergyEfficiencyClass": "k", "ConstructionYear": "1996"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:e3784a8d-53fb-44d0-b714-c832770b708f ibpdi:hasBuilding inst:7111ddf7-10b7-4a6b-8ad9-8beccef16f10.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "e3784a8d-53fb-44d0-b714-c832770b708f", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Opernving", "HouseNumber": "13", "PostalCode": "1010", "City": "Wien", "Country": "Austria"}}, {"id": "7111ddf7-10b7-4a6b-8ad9-8beccef16f10", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000061", "Name": "Hotel \"Le Méridien\"", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2019"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7643f508-bea8-49c4-b403-ff72b62921bb ibpdi:hasBuilding inst:cccc8903-cbd3-4636-b0ea-992c16c004d3.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7643f508-bea8-49c4-b403-ff72b62921bb", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Knoten NuBdor Tragwerk", "HouseNumber": "1982", "PostalCode": "1190", "City": "Wien", "Country": "Austria"}}, {"id": "cccc8903-cbd3-4636-b0ea-992c16c004d3", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000062", "Name": "Business Center Muthgasse", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "228", "EnergyEfficiencyClass": "k", "ConstructionYear": "2002"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:4ee0b1b7-86d5-4e77-a9a9-93259cfef09c ibpdi:hasBuilding inst:1ccb4dde-ccc2-4267-aa90-d682090fe9ab.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "4ee0b1b7-86d5-4e77-a9a9-93259cfef09c", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Gemngross (Modepalast Concept Store Mariahilfer Straßeaße", "HouseNumber": "42/48", "PostalCode": "1070", "City": "Wien", "Country": "Austria"}}, {"id": "1ccb4dde-ccc2-4267-aa90-d682090fe9ab", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000063", "Name": "Kaufhaus Gerngross", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "1904"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:d5b4ed3a-a1b5-4987-a0b1-1804babf924a ibpdi:hasBuilding inst:deeddaa1-108b-4c09-a75d-5d867249b53f.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "d5b4ed3a-a1b5-4987-a0b1-1804babf924a", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "PI de Catalunya Planta Oficina", "HouseNumber": "443", "PostalCode": "08002", "City": "Barcelona", "Country": "Spain"}}, {"id": "deeddaa1-108b-4c09-a75d-5d867249b53f", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000064", "Name": "El Triangle", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "293", "EnergyEfficiencyClass": "k", "ConstructionYear": "1998"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:b26ca573-f10b-4568-acfb-63a430ea1e94 ibpdi:hasBuilding inst:e9c0ca5b-02e8-4732-8c7d-86632baba8c5.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "b26ca573-f10b-4568-acfb-63a430ea1e94", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "C/ del Pintor Fortuny", "HouseNumber": "6,8,", "PostalCode": "08001", "City": "Barcelona", "Country": "Spain"}}, {"id": "e9c0ca5b-02e8-4732-8c7d-86632baba8c5", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000065", "Name": "Hotel \"Le Méridien\"", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "23", "EnergyEfficiencyClass": "k", "ConstructionYear": "1956"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:1761d76e-4804-4a9d-adaa-669822e424d0 ibpdi:hasBuilding inst:319ebfe7-3d4a-4b71-9216-e65dfb668f81.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "1761d76e-4804-4a9d-adaa-669822e424d0", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON>", "PostalCode": "28012", "City": " Madrid", "Country": "Spain"}}, {"id": "319ebfe7-3d4a-4b71-9216-e65dfb668f81", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000066", "Name": "<PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "1900"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:2cb71bb8-de4e-4479-8db3-7c9b78801f2c ibpdi:hasBuilding inst:76eb609f-9a49-4441-9258-a4cb9d63dcd1.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "2cb71bb8-de4e-4479-8db3-7c9b78801f2c", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Calle Conde de Gondomar", "PostalCode": "14003", "City": "Córdoba", "Country": "Spain"}}, {"id": "76eb609f-9a49-4441-9258-a4cb9d63dcd1", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000067", "Name": "Calle Conde de Gondomar", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "1989"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:788c3fef-e497-4d68-9111-f5eb2c63e69c ibpdi:hasBuilding inst:bcb2188b-0ce9-4ec2-8d5c-c9131e302091.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "788c3fef-e497-4d68-9111-f5eb2c63e69c", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "C de Pelai", "PostalCode": "08001", "City": "Barcelona", "Country": "Spain"}}, {"id": "bcb2188b-0ce9-4ec2-8d5c-c9131e302091", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000068", "Name": "<PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "1936"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:649fc234-610d-414b-ba34-70f124825a1f ibpdi:hasBuilding inst:d577dc63-6bf1-4c89-8d9b-f4ee302444d0.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "649fc234-610d-414b-ba34-70f124825a1f", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue de la Victoire", "HouseNumber": "53", "PostalCode": "75009", "City": "Paris", "Country": "France"}}, {"id": "d577dc63-6bf1-4c89-8d9b-f4ee302444d0", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000069", "Name": "Opéra-Victoire", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "227", "EnergyEfficiencyClass": "k", "ConstructionYear": "1998"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:c237e5e3-9476-410b-b793-321a7f912fa3 ibpdi:hasBuilding inst:975306b0-55ed-4bec-8d1d-eb7403a01369.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "c237e5e3-9476-410b-b793-321a7f912fa3", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue du Faubourg Saint-Honoré", "HouseNumber": "120", "PostalCode": "75008", "City": "Paris", "Country": "France"}}, {"id": "975306b0-55ed-4bec-8d1d-eb7403a01369", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000070", "Name": "120 Rue du Faubourg Saint-Honoré", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "26", "EnergyEfficiencyClass": "k", "ConstructionYear": "2018"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:08ab822a-72e3-4967-8eb2-acffafe469e9 ibpdi:hasBuilding inst:832cb406-5337-4132-81b6-74298751d028.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "08ab822a-72e3-4967-8eb2-acffafe469e9", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue du septembre", "HouseNumber": "4", "PostalCode": "75002", "City": "Paris", "Country": "France"}}, {"id": "832cb406-5337-4132-81b6-74298751d028", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000071", "Name": "Le Centorial", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "267", "EnergyEfficiencyClass": "k", "ConstructionYear": "1878"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:38c81806-5b93-4038-933b-7c6987835b98 ibpdi:hasBuilding inst:f77edc3d-5b2c-42bb-b7e9-8efb5c218cb2.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "38c81806-5b93-4038-933b-7c6987835b98", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue dela Gare", "HouseNumber": "35", "PostalCode": "75019", "City": "Paris", "Country": "France"}}, {"id": "f77edc3d-5b2c-42bb-b7e9-8efb5c218cb2", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000072", "Name": "35 Rue de la Gare", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "412", "EnergyEfficiencyClass": "k", "ConstructionYear": "2007"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:fb06d8da-e554-4132-b1fd-00462f3ec118 ibpdi:hasBuilding inst:444f8198-cfe9-4eec-ae72-f690224d8881.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "fb06d8da-e554-4132-b1fd-00462f3ec118", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue des Hussiers", "HouseNumber": "1", "PostalCode": "92200", "City": "Neuilly-sur-Seine", "Country": "France"}}, {"id": "444f8198-cfe9-4eec-ae72-f690224d8881", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000073", "Name": "1 Rue des Huissiers", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "294", "EnergyEfficiencyClass": "k", "ConstructionYear": "1972"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:16521686-eb85-4515-86f4-a6bac3c82392 ibpdi:hasBuilding inst:0fb819dd-8215-4224-be62-f8bc93aab237.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "16521686-eb85-4515-86f4-a6bac3c82392", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Now Coworking Rue de Marseille", "HouseNumber": "35", "PostalCode": "69007", "City": "Lyon", "Country": "France"}}, {"id": "0fb819dd-8215-4224-be62-f8bc93aab237", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000074", "Name": "New Deal", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "165", "EnergyEfficiencyClass": "k", "ConstructionYear": "1932"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:4c76dfb7-9af2-4a0b-9a1a-5c4f93c48231 ibpdi:hasBuilding inst:f52ac78d-3bb0-4c2a-9777-39f65ccf1716.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "4c76dfb7-9af2-4a0b-9a1a-5c4f93c48231", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Utoqual", "HouseNumber": "29", "PostalCode": "8008", "City": "Zürich", "Country": "Switzerland"}}, {"id": "f52ac78d-3bb0-4c2a-9777-39f65ccf1716", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000075", "Name": "Utoquai 29", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "1900"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:2fd1e79c-ba78-4324-90dd-0f19b9fa2a5f ibpdi:hasBuilding inst:9003555b-57c5-426a-b8e5-31fcdc035e5b.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "2fd1e79c-ba78-4324-90dd-0f19b9fa2a5f", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Jewry", "HouseNumber": "101d", "PostalCode": "EC2R 8DN", "City": "London", "Country": "UK"}}, {"id": "9003555b-57c5-426a-b8e5-31fcdc035e5b", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000076", "Name": "Old Jewry", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2008"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:6ae41207-b687-4503-8ee6-5e6c14283f21 ibpdi:hasBuilding inst:4e5596f9-f928-47a8-907f-2538d291e0be.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "6ae41207-b687-4503-8ee6-5e6c14283f21", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Moor House London Wall", "HouseNumber": "120", "PostalCode": "EC2Y 5ET", "City": "London ", "Country": "UK"}}, {"id": "4e5596f9-f928-47a8-907f-2538d291e0be", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000077", "Name": "Moor House", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "15", "EnergyEfficiencyClass": "k", "ConstructionYear": "2005"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:0b923e2c-257c-4ddd-9f96-2fea85b3ef65 ibpdi:hasBuilding inst:1ba5c96e-d666-4981-8962-b62d195f4eca.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "0b923e2c-257c-4ddd-9f96-2fea85b3ef65", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Southampton Row", "HouseNumber": "1", "PostalCode": "WC1B 5HA", "City": "London ", "Country": "UK"}}, {"id": "1ba5c96e-d666-4981-8962-b62d195f4eca", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000078", "Name": "One Southampton Row", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "6", "EnergyEfficiencyClass": "k", "ConstructionYear": "1900"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:a7168c52-d954-4b0d-bc32-aed52b494de4 ibpdi:hasBuilding inst:96c6d1fb-8146-42fc-bbf1-5ac0f10926c7.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "a7168c52-d954-4b0d-bc32-aed52b494de4", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Palestra House Blackfriars Ré", "HouseNumber": "197", "PostalCode": "SE1 8JZ", "City": "London ", "Country": "UK"}}, {"id": "96c6d1fb-8146-42fc-bbf1-5ac0f10926c7", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000079", "Name": "Palestra", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "30", "EnergyEfficiencyClass": "k", "ConstructionYear": "2006"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:afbeaae2-a451-4f0a-a7d2-615f48f3b2a6 ibpdi:hasBuilding inst:66ef33d4-3f94-4a03-8f2e-6d9ec801ae53.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "afbeaae2-a451-4f0a-a7d2-615f48f3b2a6", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Aldermanbury Barbican", "HouseNumber": "5", "PostalCode": "EC2V 7BP", "City": "London ", "Country": "UK"}}, {"id": "66ef33d4-3f94-4a03-8f2e-6d9ec801ae53", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000080", "Name": "Aldermanbury Square", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "18", "EnergyEfficiencyClass": "k", "ConstructionYear": "2007"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:ad1c0f03-9827-45dd-a93a-2a0732e0af2f ibpdi:hasBuilding inst:9be56927-0ba1-4397-b8e2-db496d7528f8.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "ad1c0f03-9827-45dd-a93a-2a0732e0af2f", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Houndsditch", "HouseNumber": "138", "PostalCode": "EC3A 7AW", "City": "London ", "Country": "UK"}}, {"id": "9be56927-0ba1-4397-b8e2-db496d7528f8", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000081", "Name": "The St. Botolph Building", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "14", "EnergyEfficiencyClass": "k", "ConstructionYear": "2010"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7b172331-43e3-4351-9675-3067b19aae30 ibpdi:hasBuilding inst:b5f9f964-0241-4db9-9ca1-2fc469650560.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7b172331-43e3-4351-9675-3067b19aae30", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Margaret St", "HouseNumber": "34-36", "PostalCode": "W1G 0JE", "City": "London ", "Country": "UK"}}, {"id": "b5f9f964-0241-4db9-9ca1-2fc469650560", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000082", "Name": "Margaret Street 33", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "110", "EnergyEfficiencyClass": "k", "ConstructionYear": "2013"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:db63f613-1f33-43e4-aeb0-b1e41c5bbb5d ibpdi:hasBuilding inst:2df597b6-5476-42ef-8aad-7796870ca628.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "db63f613-1f33-43e4-aeb0-b1e41c5bbb5d", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Atria One Morrison St", "HouseNumber": "144", "PostalCode": "EH3 8EX", "City": "Edinburgh ", "Country": "UK"}}, {"id": "2df597b6-5476-42ef-8aad-7796870ca628", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000083", "Name": "Atria One & Two", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "20", "EnergyEfficiencyClass": "k", "ConstructionYear": "2013"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:58148b74-42e6-4f2e-a8b9-5c82e4bce54e ibpdi:hasBuilding inst:be3982d9-1086-4de2-a5be-614a95e13330.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "58148b74-42e6-4f2e-a8b9-5c82e4bce54e", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON> treet", "PostalCode": "MK17 8EW", "City": "Milton Keynes", "Country": "UK"}}, {"id": "be3982d9-1086-4de2-a5be-614a95e13330", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000084", "Name": "<PERSON> MP2", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2014"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:a72447d7-8f89-416a-a7df-fb025553b122 ibpdi:hasBuilding inst:2e503f44-0591-486c-b333-f326ed980a21.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "a72447d7-8f89-416a-a7df-fb025553b122", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Generata Jévefa <PERSON>", "HouseNumber": "2", "PostalCode": "50-265", "City": "Wrocław", "Country": "Poland"}}, {"id": "2e503f44-0591-486c-b333-f326ed980a21", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000085", "Name": "Bema Plaza", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "397", "EnergyEfficiencyClass": "k", "ConstructionYear": "2007"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:128ffd41-54ff-4045-b654-54ed4c12b7de ibpdi:hasBuilding inst:c2cb5e24-bff4-4bc9-aaba-4d6de75dce3a.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "128ffd41-54ff-4045-b654-54ed4c12b7de", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON>", "HouseNumber": "11", "PostalCode": "96-500", "Country": "Poland"}}, {"id": "c2cb5e24-bff4-4bc9-aaba-4d6de75dce3a", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Industrial, Distribution Warehouse", "BuildingCode": "1000000086", "Name": "Tesco Distribution Center", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2004"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:64eeb72f-abac-48a6-bf44-1e9e403bb579 ibpdi:hasBuilding inst:c159db6c-d0a5-4298-baff-f65a2d735bcc.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "64eeb72f-abac-48a6-bf44-1e9e403bb579", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Towarowa", "HouseNumber": "28", "PostalCode": "00-847", "City": "Warszawa", "Country": "Poland"}}, {"id": "c159db6c-d0a5-4298-baff-f65a2d735bcc", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000087", "Name": "Generation Park Z", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "110", "EnergyEfficiencyClass": "k", "ConstructionYear": "2019"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:726681f4-2e71-4cd5-9498-97bc5627ca66 ibpdi:hasBuilding inst:a8fa7d8c-dd49-4d11-9379-3d2d6ab2d67e.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "726681f4-2e71-4cd5-9498-97bc5627ca66", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Ka<PERSON><PERSON><PERSON>", "HouseNumber": "26", "PostalCode": "76133", "City": "Karlsruhe", "Country": "Germany"}}, {"id": "a8fa7d8c-dd49-4d11-9379-3d2d6ab2d67e", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000088", "Name": "<PERSON><PERSON><PERSON> Tor Karlsruhe", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "846", "EnergyEfficiencyClass": "C", "ConstructionYear": "2005"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:f1901c05-ebe4-4109-b0b4-540e84878344 ibpdi:hasBuilding inst:e20a7cde-b6df-4ec1-9c21-00cb619c66a6.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "f1901c05-ebe4-4109-b0b4-540e84878344", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON>", "HouseNumber": "14", "PostalCode": "1082", "City": "LS Amsterdam", "Country": "Netherlands"}}, {"id": "e20a7cde-b6df-4ec1-9c21-00cb619c66a6", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000089", "Name": "<PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2005"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:3d6a0fbc-c197-4bd0-9c26-c7d1c5767f5b ibpdi:hasBuilding inst:6edcfd7b-47ed-46f1-9a91-c3e99a082b92.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "3d6a0fbc-c197-4bd0-9c26-c7d1c5767f5b", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue du Progrés", "HouseNumber": "55", "PostalCode": "1210", "City": "Saint-Josse-ten-Noode", "Country": "Belgium"}}, {"id": "6edcfd7b-47ed-46f1-9a91-c3e99a082b92", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000090", "Name": "<PERSON><PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "405", "EnergyEfficiencyClass": "k", "ConstructionYear": "2000"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:8342681e-bfbb-4315-8798-908dc9b5f2e9 ibpdi:hasBuilding inst:58929013-f385-45cc-83e6-bbb1b329818f.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "8342681e-bfbb-4315-8798-908dc9b5f2e9", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue dela Lol", "HouseNumber": "107", "PostalCode": "1040", "City": "Bruxelles", "Country": "Belgium"}}, {"id": "58929013-f385-45cc-83e6-bbb1b329818f", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000091", "Name": "The One", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "148", "EnergyEfficiencyClass": "k", "ConstructionYear": "2019"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:c9112f6e-7cc0-4164-bf33-dc5cb6850981 ibpdi:hasBuilding inst:0b8245dd-551c-45a4-bfc4-473e89ccacb5.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "c9112f6e-7cc0-4164-bf33-dc5cb6850981", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Bischoffsheimlaan", "HouseNumber": "12", "PostalCode": "1000", "City": "Brussel", "Country": "Belgium"}}, {"id": "0b8245dd-551c-45a4-bfc4-473e89ccacb5", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000092", "Name": "Spectrum", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "150", "EnergyEfficiencyClass": "k", "ConstructionYear": "2019"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:b203841a-89ff-4596-a53c-826c32ab1d9f ibpdi:hasBuilding inst:87389579-0188-4e4b-af64-10a14fce29f2.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "b203841a-89ff-4596-a53c-826c32ab1d9f", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Trabrennstraße", "HouseNumber": "6", "PostalCode": "1020", "City": "Wien", "Country": "Austria"}}, {"id": "87389579-0188-4e4b-af64-10a14fce29f2", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000093", "Name": "HochZwei", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "69", "EnergyEfficiencyClass": "k", "ConstructionYear": "2009"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:24a9dee6-6646-460d-a75b-a05d2586ae47 ibpdi:hasBuilding inst:0058c837-25b9-4345-9cc1-ec056f456052.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "24a9dee6-6646-460d-a75b-a05d2586ae47", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON>", "PostalCode": "1020", "City": "Wien", "Country": "Austria"}}, {"id": "0058c837-25b9-4345-9cc1-ec056f456052", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000094", "Name": "PlusZwei", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "430", "EnergyEfficiencyClass": "k", "ConstructionYear": "2009"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:b491d9b2-c2e6-419c-a6e6-1ab7c91f1dda ibpdi:hasBuilding inst:dfcf1ea1-69bb-416d-a393-1ca5cd58aa5a.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "b491d9b2-c2e6-419c-a6e6-1ab7c91f1dda", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Alta Diagonal", "PostalCode": "Alta Diagonal, Av. <PERSON>, 640, 6º A", "City": "Barcelona", "Country": "Spain"}}, {"id": "dfcf1ea1-69bb-416d-a393-1ca5cd58aa5a", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000095", "Name": "Alta Diagonal", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "427", "EnergyEfficiencyClass": "k", "ConstructionYear": "1993"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:86b89036-0cca-4c25-9aa2-7a0aa670f9b0 ibpdi:hasBuilding inst:b2912904-1a1e-4f3e-8a85-cc98d196c385.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "86b89036-0cca-4c25-9aa2-7a0aa670f9b0", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue He<PERSON>", "HouseNumber": "26", "PostalCode": "92130", "City": "Issy-les-Moulineaux", "Country": "France"}}, {"id": "b2912904-1a1e-4f3e-8a85-cc98d196c385", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000096", "Name": "EQWATER", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "220", "EnergyEfficiencyClass": "k", "ConstructionYear": "2010"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:f4881573-ad13-4d33-a0c4-2a9de37a4d4d ibpdi:hasBuilding inst:dfa528aa-af9f-48bd-b0a6-dd7320157c8b.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "f4881573-ad13-4d33-a0c4-2a9de37a4d4d", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON>", "HouseNumber": "54", "PostalCode": "75009", "City": "Paris", "Country": "France"}}, {"id": "dfa528aa-af9f-48bd-b0a6-dd7320157c8b", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000097", "Name": "54 <PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "73", "EnergyEfficiencyClass": "k", "ConstructionYear": "2000"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:a3408b13-da13-44c7-af5b-4353f5491be8 ibpdi:hasBuilding inst:c82e04a6-352d-4b11-8fa8-1ee6d861973e.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "a3408b13-da13-44c7-af5b-4353f5491be8", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue <PERSON>", "HouseNumber": "9-15", "PostalCode": "92130", "City": "Issy-les-Moulineaux", "Country": "France"}}, {"id": "c82e04a6-352d-4b11-8fa8-1ee6d861973e", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Retail", "BuildingCode": "1000000098", "Name": "Central Park", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "310", "EnergyEfficiencyClass": "k", "ConstructionYear": "2000"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:5ecf3188-9d58-4652-9d19-1d3794f95420 ibpdi:hasBuilding inst:0a8cbe14-3d18-4c6c-a778-abade5b4b8b1.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "5ecf3188-9d58-4652-9d19-1d3794f95420", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue du Général Foy", "HouseNumber": "10/12", "PostalCode": "75008", "City": "Paris", "Country": "France"}}, {"id": "0a8cbe14-3d18-4c6c-a778-abade5b4b8b1", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000099", "Name": "<PERSON><PERSON><PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "79", "EnergyEfficiencyClass": "k", "ConstructionYear": "2018"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:1dfae7e5-51a5-4b3d-98dc-1a12529d85ea ibpdi:hasBuilding inst:66c560f4-2f87-40af-9e3c-992d886a1d60.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "1dfae7e5-51a5-4b3d-98dc-1a12529d85ea", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON> <PERSON>", "HouseNumber": "39-41", "PostalCode": "75017", "City": "Paris", "Country": "France"}}, {"id": "66c560f4-2f87-40af-9e3c-992d886a1d60", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000100", "Name": "Hotel Renaissance Arc de Triomphe", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "38", "EnergyEfficiencyClass": "k", "ConstructionYear": "2009"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:ca9bdc08-755d-42fc-b487-741a72c853e2 ibpdi:hasBuilding inst:cbde8030-0835-401a-81bb-239aa0923056.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "ca9bdc08-755d-42fc-b487-741a72c853e2", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue de Vienne", "HouseNumber": "19", "PostalCode": "75008", "City": "Paris", "Country": "France"}}, {"id": "cbde8030-0835-401a-81bb-239aa0923056", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000101", "Name": "Solstys", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "197", "EnergyEfficiencyClass": "k", "ConstructionYear": "1911"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:65263dfa-4542-49bd-893d-e3df9c253592 ibpdi:hasBuilding inst:fb1f443f-4478-4044-a8e7-d551d75802d1.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "65263dfa-4542-49bd-893d-e3df9c253592", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue La Fayette", "HouseNumber": "33", "PostalCode": "75009", "City": "Paris", "Country": "France"}}, {"id": "fb1f443f-4478-4044-a8e7-d551d75802d1", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000102", "Name": "33 Rue La Fayette", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "301", "EnergyEfficiencyClass": "k", "ConstructionYear": "1800"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:1106fa42-028e-4f90-b4b9-4490da000d35 ibpdi:hasBuilding inst:21c44521-ea5e-4e8e-97ce-d67fd02eeac4.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "1106fa42-028e-4f90-b4b9-4490da000d35", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue des Pyramides", "HouseNumber": "22", "PostalCode": "75001", "City": "Paris", "Country": "France"}}, {"id": "21c44521-ea5e-4e8e-97ce-d67fd02eeac4", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000103", "Name": "23 Opéra", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "1860"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:1b9df382-bb91-4587-922b-0db7d83cf54d ibpdi:hasBuilding inst:fdb38a15-d380-49ae-8639-cc5861edc1a3.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "1b9df382-bb91-4587-922b-0db7d83cf54d", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Via Ferrante Aport", "HouseNumber": "8", "PostalCode": "20124", "City": "Milano MI", "Country": "Italy"}}, {"id": "fdb38a15-d380-49ae-8639-cc5861edc1a3", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000104", "Name": "Palazzo Aporti", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "241", "EnergyEfficiencyClass": "k", "ConstructionYear": "2000"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:284976c1-a297-4cee-9ef2-5d28e47f54a8 ibpdi:hasBuilding inst:90e3d68a-313d-401d-82cd-f03587ddda23.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "284976c1-a297-4cee-9ef2-5d28e47f54a8", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Via Orefici", "HouseNumber": "13", "PostalCode": "20123", "City": "Milano MI", "Country": "Italy"}}, {"id": "90e3d68a-313d-401d-82cd-f03587ddda23", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000105", "Name": "Via Orefici 13", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "1892"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:bab25ed5-5a81-44c9-83d6-eaabcb196b73 ibpdi:hasBuilding inst:5b7a76cf-3c73-4553-9c30-5d3320084283.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "bab25ed5-5a81-44c9-83d6-eaabcb196b73", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Via Dogana Po", "HouseNumber": "2a", "PostalCode": "29015", "City": "Castel San Giovanni PC", "Country": "Italy"}}, {"id": "5b7a76cf-3c73-4553-9c30-5d3320084283", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Industrial, Distribution Warehouse", "BuildingCode": "1000000106", "Name": "Logistic Park Castel San Giovanni", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2005"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:fc6678d2-3ac4-4353-87ae-4dadb8c02d15 ibpdi:hasBuilding inst:97b5f5d4-eebf-4874-b9f5-83bbf9f106fa.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "fc6678d2-3ac4-4353-87ae-4dadb8c02d15", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON>", "HouseNumber": "1", "PostalCode": "29010", "City": "San Nazzaro PC", "Country": "Italy"}}, {"id": "97b5f5d4-eebf-4874-b9f5-83bbf9f106fa", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Industrial, Distribution Warehouse", "BuildingCode": "1000000107", "Name": "Logistics Centre A/C", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2010"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:bcd00c07-4e85-428e-9d9c-0cba6b3f2a1c ibpdi:hasBuilding inst:8144ad9f-a7f8-48a4-ac31-732aa7ea02b9.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "bcd00c07-4e85-428e-9d9c-0cba6b3f2a1c", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Via Roma", "HouseNumber": "98/2", "PostalCode": "20013", "City": "Magenta MI", "Country": "Italy"}}, {"id": "8144ad9f-a7f8-48a4-ac31-732aa7ea02b9", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000108", "Name": "Via Roma 98/a", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2007"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:99f94589-009f-4322-b708-2ef93c4431c6 ibpdi:hasBuilding inst:a02777cc-ae87-43f5-8a32-87d522d24d00.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "99f94589-009f-4322-b708-2ef93c4431c6", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Via samu Amada", "HouseNumber": "2", "PostalCode": "29010", "City": "Pontenure PC", "Country": "Italy"}}, {"id": "a02777cc-ae87-43f5-8a32-87d522d24d00", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Industrial, Distribution Warehouse", "BuildingCode": "1000000109", "Name": "Logistics Centre B", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2011"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:77924ad2-527e-4745-9c45-ce51e1b164af ibpdi:hasBuilding inst:f4c3b7c3-eb91-44f3-bce1-ec2903ea58df.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "77924ad2-527e-4745-9c45-ce51e1b164af", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Via Dogana Po", "HouseNumber": "2a", "PostalCode": "29015", "City": " Castel San Giovanni PC", "Country": "Italy"}}, {"id": "f4c3b7c3-eb91-44f3-bce1-ec2903ea58df", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000110", "Name": "Via Dogana Po 2a", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2011"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:2dfe9323-7ae5-47ee-bc08-413519ce45b6 ibpdi:hasBuilding inst:adbd1c9e-49b4-4f4d-8155-6329f72b6b6d.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "2dfe9323-7ae5-47ee-bc08-413519ce45b6", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Via <PERSON>", "HouseNumber": "18,", "PostalCode": "20159", "City": "Milano MI", "Country": "Italy"}}, {"id": "adbd1c9e-49b4-4f4d-8155-6329f72b6b6d", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000111", "Name": "MAC567", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "287", "EnergyEfficiencyClass": "k", "ConstructionYear": "2010"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:d8723f3d-dc77-4539-b854-531c488a12af ibpdi:hasBuilding inst:2ce6a300-22c3-4754-a5c4-6d5630632546.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "d8723f3d-dc77-4539-b854-531c488a12af", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Via Broletto", "HouseNumber": "16", "PostalCode": "20121", "City": "Milano MI", "Country": "Italy"}}, {"id": "2ce6a300-22c3-4754-a5c4-6d5630632546", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000112", "Name": "Via Broletto 16", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "30", "EnergyEfficiencyClass": "k", "ConstructionYear": "1871"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:df4eccb4-8f5c-4cf5-8365-0e3c228c4ffe ibpdi:hasBuilding inst:6358540d-a6cf-4928-a3a9-05f7ed43f408.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "df4eccb4-8f5c-4cf5-8365-0e3c228c4ffe", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Av. <PERSON>", "HouseNumber": "38", "PostalCode": "1855", "City": "Neudorf-Weimershof ", "Country": "Luxembourg"}}, {"id": "6358540d-a6cf-4928-a3a9-05f7ed43f408", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000113", "Name": "38 Av. <PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "322", "EnergyEfficiencyClass": "k", "ConstructionYear": "2002"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:aadde3f1-ec26-4962-85b6-7e38dcb4875c ibpdi:hasBuilding inst:a451ae80-eb47-4842-a66c-5340406fe0d9.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "aadde3f1-ec26-4962-85b6-7e38dcb4875c", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue du pults Romain", "HouseNumber": "23", "PostalCode": "8070", "City": "Bert<PERSON><PERSON>", "Country": "Luxembourg"}}, {"id": "a451ae80-eb47-4842-a66c-5340406fe0d9", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000114", "Name": "Vitrum", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "540", "EnergyEfficiencyClass": "k", "ConstructionYear": "2011"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:2f643fd3-9d5b-46f6-81bc-2abd60df713a ibpdi:hasBuilding inst:ea27f06d-b7a6-43e5-aee0-8ef57cb35279.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "2f643fd3-9d5b-46f6-81bc-2abd60df713a", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rue du pults Romain", "HouseNumber": "23", "PostalCode": "8070", "City": "Bert<PERSON><PERSON>", "Country": "Luxembourg"}}, {"id": "ea27f06d-b7a6-43e5-aee0-8ef57cb35279", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000115", "Name": "Emporium", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "876", "EnergyEfficiencyClass": "k", "ConstructionYear": "2005"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:47a26dba-3230-4850-a9a0-f6c0cbc90f1e ibpdi:hasBuilding inst:7b46d5e9-cb1b-4897-bb20-14c4110629ab.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "47a26dba-3230-4850-a9a0-f6c0cbc90f1e", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "unit chaman house", "HouseNumber": "2", "PostalCode": "Moorfield", "City": "Newbridge", "Country": "Ireland"}}, {"id": "7b46d5e9-cb1b-4897-bb20-14c4110629ab", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000116", "Name": "Whitewater", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "1701", "EnergyEfficiencyClass": "k", "ConstructionYear": "2006"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:68765d34-9b52-4e8f-ade6-f34a9d7ae414 ibpdi:hasBuilding inst:6f8a060f-0954-411a-aba3-0425fc60288c.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "68765d34-9b52-4e8f-ade6-f34a9d7ae414", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "as", "PostalCode": "<PERSON><PERSON>", "City": "Cork", "Country": "Ireland"}}, {"id": "6f8a060f-0954-411a-aba3-0425fc60288c", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000117", "Name": "Mahon Point Shopping Center", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2005"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:12e4be74-2fbb-4b4a-b5b7-a9bc6c8e8a1a ibpdi:hasBuilding inst:a5271b50-54b9-4517-af00-c7c2d163a1f4.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "12e4be74-2fbb-4b4a-b5b7-a9bc6c8e8a1a", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON>", "HouseNumber": "11", "PostalCode": "North", "City": "Dublin", "Country": "Ireland"}}, {"id": "a5271b50-54b9-4517-af00-c7c2d163a1f4", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000118", "Name": "Clayton Hotel Charlemont", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "20", "EnergyEfficiencyClass": "k", "ConstructionYear": "2018"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:a9ebd1a5-73a4-4e41-bf62-81299f25b959 ibpdi:hasBuilding inst:2042576d-5152-49bd-9785-8b8f4ee75d7d.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "a9ebd1a5-73a4-4e41-bf62-81299f25b959", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "HouseNumber": "13,", "PostalCode": "00120", "City": "Helsinki", "Country": "Finland"}}, {"id": "2042576d-5152-49bd-9785-8b8f4ee75d7d", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Hotel", "BuildingCode": "1000000119", "Name": "Hotel St. George", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "1840"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:d772cb79-74f8-42e1-b4a1-0998b73fa0d0 ibpdi:hasBuilding inst:5b191f8c-9775-4bd1-9d92-c875100fd773.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "d772cb79-74f8-42e1-b4a1-0998b73fa0d0", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Oosterdokskade", "HouseNumber": "163", "PostalCode": "1011", "City": "DL Amsterdam", "Country": "Netherlands"}}, {"id": "5b191f8c-9775-4bd1-9d92-c875100fd773", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000120", "Name": "Oosterdokskade 163", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "EnergyEfficiencyClass": "k", "ConstructionYear": "2022"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:93b5c1ab-31c4-40e3-aa9d-5c24b203f420 ibpdi:hasBuilding inst:b0a3f8cc-829e-4fcd-bcbc-1b1db8d55bb0.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "93b5c1ab-31c4-40e3-aa9d-5c24b203f420", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Donau-City trae", "HouseNumber": "7/27.06", "PostalCode": "1220", "City": "Wien", "Country": "Austria"}}, {"id": "b0a3f8cc-829e-4fcd-bcbc-1b1db8d55bb0", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000121", "Name": "DC Tower", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "414", "EnergyEfficiencyClass": "k", "ConstructionYear": "2013"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:9faf0eca-cfff-4933-a8c9-52a2cfc706d7 ibpdi:hasBuilding inst:67c98538-4b7c-421c-a9af-6efc3b2b21df.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "9faf0eca-cfff-4933-a8c9-52a2cfc706d7", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "<PERSON>", "HouseNumber": "35-37", "PostalCode": "60327", "City": "Frankfurt am Main", "Country": "Germany"}}, {"id": "67c98538-4b7c-421c-a9af-6efc3b2b21df", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000122", "Name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 35-37", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "552", "EnergyEfficiencyClass": "C", "ConstructionYear": "2012"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:5baac072-c349-4548-befb-978d63ebc8e8 ibpdi:hasBuilding inst:8fa01b37-3d32-4a26-9aea-0852d3d4c191.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "5baac072-c349-4548-befb-978d63ebc8e8", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rte des Acacias", "HouseNumber": "60", "PostalCode": "1227", "City": "Carouge", "Country": "Switzerland"}}, {"id": "8fa01b37-3d32-4a26-9aea-0852d3d4c191", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000123", "Name": "Rte des Acacias 60", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "827", "EnergyEfficiencyClass": "k", "ConstructionYear": "2006"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:42212432-19cd-4864-9de2-0d3f461f7e07 ibpdi:hasBuilding inst:7310956d-6d8f-4bd7-af7b-4943aae4c2a4.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "42212432-19cd-4864-9de2-0d3f461f7e07", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "St James's Square St. James's", "HouseNumber": "8", "PostalCode": "140 00", "City": " Praha 4-<PERSON><PERSON><PERSON>", "Country": "Czechia"}}, {"id": "7310956d-6d8f-4bd7-af7b-4943aae4c2a4", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000124", "Name": "Gemini", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "484", "EnergyEfficiencyClass": "k", "ConstructionYear": "2008"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:bb18b414-c15f-4df6-84c1-53282d7d0d34 ibpdi:hasBuilding inst:654b86d9-c44b-406a-a627-fdfaa29c317d.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "bb18b414-c15f-4df6-84c1-53282d7d0d34", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Vietoria Line", "HouseNumber": "173", "PostalCode": "250", "City": "70 Postřižín-<PERSON><PERSON><PERSON>", "Country": "Czechia"}}, {"id": "654b86d9-c44b-406a-a627-fdfaa29c317d", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000125", "Name": "Tesco Distribution Center", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2006"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:635d067f-ebd5-4383-98fc-ea28ba0f2f14 ibpdi:hasBuilding inst:8e8d2928-9d12-4b2a-8e7e-5136c6dd736a.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "635d067f-ebd5-4383-98fc-ea28ba0f2f14", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Rathbone Sq", "HouseNumber": "1", "PostalCode": "140 00", "City": "139 00 Praha 4-<PERSON><PERSON><PERSON>", "Country": "Czechia"}}, {"id": "8e8d2928-9d12-4b2a-8e7e-5136c6dd736a", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000126", "Name": "City Green Court", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "236", "EnergyEfficiencyClass": "k", "ConstructionYear": "2012"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:7d033169-6efc-4f1a-8128-20e1bdfece9c ibpdi:hasBuilding inst:5f3672ed-fdea-4db2-b4cf-d06f9327d474.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "7d033169-6efc-4f1a-8128-20e1bdfece9c", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Budapest", "PostalCode": "277", "City": "45 Kozomín", "Country": "Czechia"}}, {"id": "5f3672ed-fdea-4db2-b4cf-d06f9327d474", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000127", "Name": "CTPark Prague North 1", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2010"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:d965425e-fb4e-4c22-98c2-477c20f5bac1 ibpdi:hasBuilding inst:40c031cd-6838-44e2-bd5f-8509fb2ff226.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "d965425e-fb4e-4c22-98c2-477c20f5bac1", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Stortingsgata", "HouseNumber": "6", "PostalCode": "277", "City": "45 Kozomín", "Country": "Czechia"}}, {"id": "40c031cd-6838-44e2-bd5f-8509fb2ff226", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000128", "Name": "CTPark Prague North 2", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2018"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:dd4bb631-939a-45c0-a766-19c050c115af ibpdi:hasBuilding inst:2b5382c5-80cc-4151-a607-38c4a68ed3f7.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "dd4bb631-939a-45c0-a766-19c050c115af", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "pl. Wiadysta<PERSON> Andersa", "HouseNumber": "3", "PostalCode": "SW1Y 4JU", "City": "London ", "Country": "UK"}}, {"id": "2b5382c5-80cc-4151-a607-38c4a68ed3f7", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000129", "Name": "8 St James's Square, St. James's,", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "6", "EnergyEfficiencyClass": "k", "ConstructionYear": "2015"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:fcde3cfb-9806-48b3-99c8-34f08dda3dc7 ibpdi:hasBuilding inst:b0d2d212-2722-4e01-a3c2-721dc3eaed93.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "fcde3cfb-9806-48b3-99c8-34f08dda3dc7", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Upowa", "HouseNumber": "3", "PostalCode": "SW1E 5ER", "City": "London ", "Country": "UK"}}, {"id": "b0d2d212-2722-4e01-a3c2-721dc3eaed93", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000130", "Name": "173 Victoria Line", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "1960"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:fcbc08f6-9fb1-4273-be1e-48045f388881 ibpdi:hasBuilding inst:ec3dc6d1-f6ef-4099-909f-ab897b7583ac.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "fcbc08f6-9fb1-4273-be1e-48045f388881", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Greybowska SA", "PostalCode": "W1T 1FB", "City": "London ", "Country": "UK"}}, {"id": "ec3dc6d1-f6ef-4099-909f-ab897b7583ac", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000131", "Name": "One Rathbone Square", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "0", "EnergyEfficiencyClass": "k", "ConstructionYear": "2017"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:2d38d186-ea79-45de-bd78-6bd20df18893 ibpdi:hasBuilding inst:0b3dfe7c-08fd-4139-8fc4-f99d963cd23c.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "2d38d186-ea79-45de-bd78-6bd20df18893", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Polna", "HouseNumber": "7", "PostalCode": "1054", "City": "Budapest", "Country": "Hungary"}}, {"id": "0b3dfe7c-08fd-4139-8fc4-f99d963cd23c", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000132", "Name": "<PERSON><PERSON><PERSON>", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "110", "EnergyEfficiencyClass": "k", "ConstructionYear": "1998"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:5d003703-9249-494a-9d9e-51c1519178dc ibpdi:hasBuilding inst:cd9ae8c1-0380-4ecc-98a3-5510442e3b2c.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "5d003703-9249-494a-9d9e-51c1519178dc", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"StreetName": "Stortingsgata ", "HouseNumber": "6", "PostalCode": "0161", "City": "Oslo", "Country": "Norway"}}, {"id": "cd9ae8c1-0380-4ecc-98a3-5510442e3b2c", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000133", "Name": "Stortingsgata 6", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "22", "EnergyEfficiencyClass": "k", "ConstructionYear": "2004"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:c8baecf2-ab5f-44c3-b3b9-3251dbb049db ibpdi:hasBuilding inst:bb24716d-c0d1-4772-96f7-0a69002d759f.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "c8baecf2-ab5f-44c3-b3b9-3251dbb049db", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"PostalCode": "61-894", "City": "Poznań", "Country": "Poland"}}, {"id": "bb24716d-c0d1-4772-96f7-0a69002d759f", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000134", "Name": "Andersia Tower", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "217", "EnergyEfficiencyClass": "k", "ConstructionYear": "2007"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:3111e590-210c-4593-926e-6700aa0f4514 ibpdi:hasBuilding inst:0a2add59-d7d2-4802-ac6d-2bbe4f1a4575.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "3111e590-210c-4593-926e-6700aa0f4514", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"PostalCode": "44-102", "City": "<PERSON><PERSON><PERSON><PERSON>", "Country": "Poland"}}, {"id": "0a2add59-d7d2-4802-ac6d-2bbe4f1a4575", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000135", "Name": "Forum Gliwice", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "823", "EnergyEfficiencyClass": "k", "ConstructionYear": "2007"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:333666e7-9f66-4408-b40d-f16af038ef88 ibpdi:hasBuilding inst:de3e19d1-687f-4ffe-83f5-96363f23275d.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "333666e7-9f66-4408-b40d-f16af038ef88", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"PostalCode": "00-132", "City": "Warszawa", "Country": "Poland"}}, {"id": "de3e19d1-687f-4ffe-83f5-96363f23275d", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000136", "Name": "Grzybowska Park", "ValidFrom": "44926", "PrimaryHeatingType": "District heating", "ParkingSpaces": "66", "EnergyEfficiencyClass": "k", "ConstructionYear": "2009"}}]}, {"graphTemplate": "@prefix ex: <http://example.org/> .\nNode1 hasRelation Node2 .", "graphData": "@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>.\r\n@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#>.\r\n@prefix xsd: <http://www.w3.org/2001/XMLSchema#>.\r\n@prefix ibpdi: <https://ibpdi.datacat.org/class/>.\r\n@prefix owl: <http://www.w3.org/2002/07/owl#>.\r\n@prefix inst: <https://example.com/>.\r\n\r\ninst:e307b8cf-9975-4889-bba5-36524843e3c3 ibpdi:hasBuilding inst:d5ccdae6-a1b9-4d9c-8657-00aa64c5d7a8.\r\n", "accessRights": null, "useCase": null, "graphMetadata": [{"id": "e307b8cf-9975-4889-bba5-36524843e3c3", "classType": "https://ibpdi.datacat.org/class/Address", "propertiesValues": {"PostalCode": "00-622", "City": "Warszawa", "Country": "Poland"}}, {"id": "d5ccdae6-a1b9-4d9c-8657-00aa64c5d7a8", "classType": "https://ibpdi.datacat.org/class/Building", "propertiesValues": {"PrimaryTypeOfBuilding": "Office", "BuildingCode": "1000000137", "Name": "IBC", "ValidFrom": "44926", "PrimaryHeatingType": "Natural gas", "ParkingSpaces": "278", "EnergyEfficiencyClass": "k", "ConstructionYear": "2003"}}]}]